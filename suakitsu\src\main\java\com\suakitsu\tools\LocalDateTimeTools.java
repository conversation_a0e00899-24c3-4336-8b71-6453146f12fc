package com.suakitsu.tools;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;

/**
 * 日期时间工具类
 */
public class LocalDateTimeTools {
    /**
     * 获取强制过期的软删除到期时间
     *
     * @return 1970-01-01
     */
    public static LocalDateTime getDeletedTime() {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(0), ZoneId.systemDefault());
    }

    /**
     * 通过时间日期获取到时间戳（UTC+8）
     *
     * @param localDateTime 需要转换的时间
     * @return 时间戳时间（毫秒）
     */
    public static Long toTimestamp(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }
}