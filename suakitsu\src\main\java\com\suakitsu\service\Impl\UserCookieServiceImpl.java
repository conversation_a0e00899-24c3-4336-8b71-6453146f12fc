package com.suakitsu.service.Impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suakitsu.commonexception.UserLoginException;
import com.suakitsu.entity.UserCookie;
import com.suakitsu.mapper.UserCookieMapper;
import com.suakitsu.service.UserCookieService;
import com.suakitsu.utils.LocalDateTimeUtil;
import com.suakitsu.utils.StringUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
public class UserCookieServiceImpl extends ServiceImpl<UserCookieMapper, UserCookie> implements UserCookieService {
    @Resource
    HttpServletRequest request;

    /**
     * 通过请求获取用户ID
     *
     * @return 返回登录的用户ID
     */
    public Long getUserInfo() {
        return this.getUserInfo(this.request);
    }

    /**
     * 获取登录的用户ID
     *
     * @param request 请求信息
     * @return 返回登录的用户ID
     */
    public Long getUserInfo(HttpServletRequest request) {
        try {
            // 获取cookie对应的用户
            String authCookie = this.getRequestCookie(request);
            UserCookie userCookie = baseMapper.selectFirst(new LambdaQueryWrapper<UserCookie>()
                    .eq(UserCookie::getCookie, authCookie)
                    .ge(UserCookie::getExpirationTime, LocalDateTime.now()));
            Assert.notNull(userCookie, "登陆状态无效");
            return userCookie.getUserId();
        } catch (Exception e) {
            throw new UserLoginException();
        }
    }


    /**
     * 获取请求的Cookie
     *
     * @return Cookie
     */
    private String getRequestCookie(HttpServletRequest request) {
        // 解析请求Cookie
        Map<String, String> cookies = new HashMap<>();
        for (Cookie cookie : request.getCookies()) {
            cookies.put(cookie.getName(), cookie.getValue());
        }
        String authCookie = cookies.get("Authorization");
        Assert.hasText(authCookie, "获取cookie失败");
        return authCookie;
    }

    /**
     * 添加用户登录信息
     *
     * @param userId     用户ID
     * @param isRemember 是否保持登录
     * @return Cookie
     */
    public String addCookie(Long userId, boolean isRemember) {
        // 保存Cookie并返回
        String cookie = StringUtil.random(128);
        UserCookie userCookie = (UserCookie) new UserCookie()
                .setUserId(userId)
                .setCookie(cookie)
                .setExpirationTime(LocalDateTime.now().plusDays(15))
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());
        baseMapper.insert(userCookie);
        return cookie;
    }

    /**
     * 退出登录（无视登录状态）
     */
    public void deleteCookie() {
        try {
            baseMapper.update(new LambdaUpdateWrapper<UserCookie>()
                    .eq(UserCookie::getCookie, this.getRequestCookie(this.request))
                    .ge(UserCookie::getExpirationTime, LocalDateTime.now())
                    .set(UserCookie::getExpirationTime, LocalDateTimeUtil.getDeletedTime()));
        } catch (Exception ignore) {

        }
    }
}