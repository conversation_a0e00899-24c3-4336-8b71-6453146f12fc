{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__1F0BDA5", "name": "GuiYuan", "version": {"name": "1.0.0", "code": "100"}, "description": "", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "network": {"request": {"timeout": 10000, "sslVerify": false}}, "distribute": {"icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"minSdkVersion": 21, "targetSdkVersion": 33, "permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#F8F8F8", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.45", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#F0AD4E", "selectedColor": "#3cc51f", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "50px", "list": [{"pagePath": "pages/tabbar/index/index", "iconPath": "/static/index/index.png", "selectedIconPath": "/static/index/indexselect.png", "text": "首页"}, {"pagePath": "pages/tabbar/daily/daily", "iconPath": "/static/daily/daily.png", "selectedIconPath": "/static/daily/dailyselect.png", "text": "日常"}, {"pagePath": "pages/tabbar/message/message", "iconPath": "/static/message/message.png", "selectedIconPath": "/static/message/messageselect.png", "text": "消息"}, {"pagePath": "pages/tabbar/home/<USER>", "iconPath": "/static/home/<USER>", "selectedIconPath": "/static/home/<USER>", "text": "主页"}], "backgroundColor": "#F8F8F8", "selectedIndex": 0, "shown": true, "child": ["lauchwebview"], "selected": 0}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#F8F8F8", "bottom": {"offset": "auto"}}}, "launch_path": "__uniappview.html"}