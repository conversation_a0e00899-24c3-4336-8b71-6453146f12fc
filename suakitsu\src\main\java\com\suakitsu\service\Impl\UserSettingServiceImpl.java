package com.suakitsu.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suakitsu.entity.UserSetting;
import com.suakitsu.mapper.UserSettingMapper;
import com.suakitsu.service.UserSettingService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户设置服务实现类
 */
@Service
public class UserSettingServiceImpl extends ServiceImpl<UserSettingMapper, UserSetting> implements UserSettingService {

    @Resource
    private UserSettingMapper userSettingMapper;

    /**
     * 获取用户设置
     * @param userId 用户ID
     * @return 用户设置信息
     */
    @Override
    public UserSetting getUserSetting(Long userId) {
        return userSettingMapper.selectOne(new LambdaQueryWrapper<UserSetting>()
                .eq(UserSetting::getUserId, userId));
    }

    /**
     * 创建或更新用户设置
     * @param userSetting 用户设置信息
     * @return 是否成功
     */
    @Override
    public boolean saveOrUpdateUserSetting(UserSetting userSetting) {
        // 设置时间
        LocalDateTime now = LocalDateTime.now();
        userSetting.setUpdateTime(now);
        
        // 查询是否已存在
        UserSetting existingSetting = getUserSetting(userSetting.getUserId());
        
        if (existingSetting == null) {
            // 不存在则创建
            userSetting.setCreateTime(now);
            return userSettingMapper.insert(userSetting) > 0;
        } else {
            // 存在则更新
            userSetting.setId(existingSetting.getId());
            return userSettingMapper.updateById(userSetting) > 0;
        }
    }

    /**
     * 删除用户设置
     * @param userId 用户ID
     * @return 是否成功
     */
    @Override
    public boolean deleteUserSetting(Long userId) {
        return userSettingMapper.delete(new LambdaQueryWrapper<UserSetting>()
                .eq(UserSetting::getUserId, userId)) > 0;
    }
}