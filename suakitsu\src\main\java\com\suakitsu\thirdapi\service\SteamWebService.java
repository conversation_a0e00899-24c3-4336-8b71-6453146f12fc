package com.suakitsu.thirdapi.service;

import com.suakitsu.commonexception.ApiResultException;
import com.suakitsu.entity.User;
import com.suakitsu.thirdapi.model.ConfirmHash;
import com.suakitsu.thirdapi.model.MobileConfList;
import com.suakitsu.thirdapi.model.SteamMaFile;
import com.suakitsu.utils.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Service
public class SteamWebService {

    private SteamMaFile steamMaFile;

    private final ObjectMapper objectMapper = new ObjectMapper();
    // tokenCode  secret  为ma文件的shared_secret

    // 令牌计算
    public static String getSteamAuthCode(String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        long time = System.currentTimeMillis() / 30000; // Equivalent to Python's time() / 30
        System.out.println(time);
        ByteBuffer buffer = ByteBuffer.allocate(8).order(ByteOrder.BIG_ENDIAN);
        buffer.putLong(time);
        byte[] packedTime = buffer.array();
        byte[] msg = new byte[8];
        System.arraycopy(packedTime, 0, msg, 0, 8);
        byte[] decode = Base64.getDecoder().decode(secret);// Equivalent to Python's b64encode
        Mac sha1_HMAC = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(decode, "HmacSHA1");
        sha1_HMAC.init(secretKeySpec); // Assuming secret is a valid Base64 encoded string
        byte[] mac = sha1_HMAC.doFinal(msg);
        int offset = mac[mac.length - 1] & 0x0f; // Equivalent to Python's & 0x0f
        int binary = ByteBuffer.wrap(mac, offset, 4).order(ByteOrder.BIG_ENDIAN).getInt() & 0x7fffffff; // Equivalent to Python's struct unpack and & 0x7fffffff
        char[] codestr = "23456789BCDFGHJKMNPQRTVWXY".toCharArray(); // This part seems unnecessary as the original Python code only uses it once, so I'll assume this is correct for now
        char[] chars = new char[5];
        for (int i = 0; i < 5; i++) {
            chars[i] = codestr[binary % 26]; // Equivalent to Python's % 26 and indexing
            binary /= 26; // Equivalent to Python's //= 26
        }
        return new String(chars);
    }
    // 获取令牌列表
    public static Map<String, Long> getTokenList(String tokenCode) throws NoSuchAlgorithmException {
        String B64KEY = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
        String CHARS = "23456789BCDFGHJKMNPQRTVWXY";

        // 计算解码后令牌并定义key
        int padding = tokenCode.contains("=") ? tokenCode.length() - tokenCode.indexOf("=") : 0;
        byte[] decodedSecret = new byte[(tokenCode.length() / 4) * 3 - padding];
        int i = 0, j = 0;
        while (i < tokenCode.length()) {
            int enc1 = B64KEY.indexOf(tokenCode.charAt(i++));
            int enc2 = B64KEY.indexOf(tokenCode.charAt(i++));
            int enc3 = B64KEY.indexOf(tokenCode.charAt(i++));
            int enc4 = B64KEY.indexOf(tokenCode.charAt(i++));

            decodedSecret[j++] = (byte) ((enc1 << 2) | (enc2 >> 4));
            if (enc3 != 64) decodedSecret[j++] = (byte) (((enc2 & 15) << 4) | (enc3 >> 2));
            if (enc4 != 64) decodedSecret[j++] = (byte) (((enc3 & 3) << 6) | enc4);
        }
        SecretKeySpec key = new SecretKeySpec(decodedSecret, "HmacSHA1");
        Mac mac= Mac.getInstance("HmacSHA1");
        try {
            mac = Mac.getInstance("HmacSHA1");
            mac.init(key);
        } catch (Exception ignore) {

        }

        // 从当前时间开始，计算之后的令牌，每次偏移30秒
        Map<String, Long> tokenMap = new HashMap<>();
        int nowTime = (int) (System.currentTimeMillis() / 30000);
        for (int s = 0; s < 5; s += 1) {
            // 处理时间
            int authTime = nowTime + s;
            ByteBuffer timeIndexBuffer = ByteBuffer.allocate(8);
            timeIndexBuffer.putInt(4, authTime);

            // 初始化计算
            byte[] hmac = mac.doFinal(timeIndexBuffer.array());
            int start = hmac[19] & 0x0F;
            int rawCode = ByteBuffer.wrap(hmac).getInt(start) & 0x7FFFFFFF;

            // 计算令牌值
            StringBuilder authCode = new StringBuilder();
            for (int k = 0; k < 5; k++) {
                authCode.append(CHARS.charAt(rawCode % CHARS.length()));
                rawCode /= CHARS.length();
            }

            // 写到结果
            tokenMap.put(authCode.toString(), authTime * 30 * 1000L);
        }
        return tokenMap;
    }
    /**
     * 获取所有待手机确认的订单
     * @return
     */
    public List<MobileConfList.ConfDTO> confirmOfferList() {
        HttpUtil.ProxyRequestVo.ProxyRequestVoBuilder builder = HttpUtil.ProxyRequestVo.builder();
        builder.url("https://steamcommunity.com/mobileconf/getlist").steamCookieId("76561199400266084");
        Map<String, String> query = new HashMap<>();
        query.put("p", "android:ae5f81bf-4d12-491b-81d4-51b7ad84dd57");
        query.put("a", "76561199400266084");
        try {
            ConfirmHash confirmHash = generateConfirmationHashForTime("conf");
            query.put("k", confirmHash.getHash());
            query.put("t", String.valueOf(confirmHash.getTime()));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        query.put("m", "react");
        query.put("tag", "conf");
        builder.query(query);
        Map<String, String> header = new HashMap<>();
        header.put("Accept-Language", "zh-CN,zh;q=0.9");
        builder.headers(header);
        MobileConfList json;
        try {
            User user = new User();
            //TODO 修改IP
            HttpUtil.ProxyResponseVo proxyResponseVo = HttpUtil.sentToSteamByProxy(builder.build(), Optional.of(user));
            if(Objects.isNull(proxyResponseVo.getStatus()) || proxyResponseVo.getStatus()!=200){
                throw new ApiResultException(-1, "交易失败");
            }
            json = objectMapper.readValue(proxyResponseVo.getHtml(), MobileConfList.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            throw new ApiResultException(-1, "交易失败服务器返回内容不可识别");
        }
        return json.getConf();
    }
    /**
     * 生成确认列表签名
     *
     * @param tag
     * @return
     */
    private ConfirmHash generateConfirmationHashForTime(String tag) throws NoSuchAlgorithmException, InvalidKeyException {
        long time = System.currentTimeMillis() / 1000L;
        byte[] arr = new byte[8];
        // 将 long 类型的 time 转换为 8 个字节的大端序
        for (int i = 0; i < 8; i++) {
            arr[i] = (byte) (time >> ((7 - i) * 8));
        }

        // 将 tag 转换为 UTF-8 编码的字节数组，并确保长度不超过 32 字节
        byte[] tagBytes = tag.getBytes(StandardCharsets.UTF_8);
        if (tagBytes.length > 32) {
            tagBytes = Arrays.copyOf(tagBytes, 32);
        }

        // 将时间字节和标签字节合并
        byte[] data = new byte[arr.length + tagBytes.length];
        System.arraycopy(arr, 0, data, 0, arr.length);
        System.arraycopy(tagBytes, 0, data, arr.length, tagBytes.length);

        // 创建 HMAC-SHA1 Mac 实例
        Mac sha1_HMAC = Mac.getInstance("HmacSHA1");
        byte[] decode = Base64.getDecoder().decode(steamMaFile.getIdentitySecret());
        SecretKeySpec secret_key = new SecretKeySpec(decode, "HmacSHA1");
        sha1_HMAC.init(secret_key);

        // 计算 HMAC 并返回 Base64 编码的哈希值
        byte[] hashedData = sha1_HMAC.doFinal(data);
        ConfirmHash confirmHash = new ConfirmHash();
        confirmHash.setHash(Base64.getEncoder().encodeToString(hashedData));
        confirmHash.setTime(time);
        return confirmHash;
    }

}
