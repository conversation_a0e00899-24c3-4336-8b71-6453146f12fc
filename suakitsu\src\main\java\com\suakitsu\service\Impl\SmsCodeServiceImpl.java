package com.suakitsu.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.qcloudsms.SmsSingleSender;
import com.github.qcloudsms.SmsSingleSenderResult;
import com.github.qcloudsms.httpclient.HTTPException;
import com.suakitsu.commonexception.ApiResultException;
import com.suakitsu.config.AppConfig;
import com.suakitsu.entity.SmsCode;
import com.suakitsu.mapper.SmsCodeMapper;
import com.suakitsu.service.SmsCodeService;
import com.suakitsu.tools.RandomUtils;
import com.suakitsu.utils.HttpServletRequestUtil;
import com.suakitsu.utils.LocalDateTimeUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class SmsCodeServiceImpl extends ServiceImpl<SmsCodeMapper, SmsCode> implements SmsCodeService {
    @Resource
    AppConfig appConfig;

    private static final int appid = 1400918436;
    private static final String appKey = "25cef7ce0d05714acc1afc1ee47e4b67";
    private static final int templateId = 2196203;
    private static final String smsSign = "烙米love个人网";

    @Resource
    HttpServletRequest request;
    /**
     * 发送短信
     * @param phone 待发送验证码的手机号
     */
    public Object sendSmsCode(String phone) throws HTTPException, IOException {
        // 发送6为短信验证码
        String randomNumber = RandomUtils.RandomNumber(6); // 注意这里应该返回String类型
        String[] params = new String[]{randomNumber};
        SmsSingleSender singleSender = new SmsSingleSender(appid, appKey);
        // 发送短信
        SmsSingleSenderResult s = singleSender.sendWithParam("86", phone, templateId, params, smsSign, "", "");
        System.out.println(s);
        if (s.result != 0){
            return s.errMsg;
        }
        int type = 0;
        this.addSmsCode(phone, type);
        return "";
    }

    /**
     * 检查发送短信验证码状态
     */
    private void checkSendSmsCodeStatus(String phone) {
        // 发信条数判断
        int maxSend = appConfig.getSmsConfig().getMaxSend();
        List<SmsCode> codeHistory = baseMapper.selectList(new LambdaQueryWrapper<SmsCode>()
                .and(LambdaQueryWrapper -> LambdaQueryWrapper
                        .eq(SmsCode::getPhone, phone))
                .ge(SmsCode::getCreateTime, LocalDate.now())
                .orderByDesc(SmsCode::getId)
                .last(String.format("limit %d", maxSend)));
        if (codeHistory.size() == maxSend) {
            throw new ApiResultException("今日发送验证码过多，请明日再试");
        }
        // 发信间隔判断
        if (!codeHistory.isEmpty() &&
                Duration.between(codeHistory.getFirst().getCreateTime(), LocalDateTime.now()).toSeconds() <
                        appConfig.getSmsConfig().getInterval().toSeconds()) {
            throw new ApiResultException("发信过于频繁，请稍后再试");
        }
    }

    /**
     * 短信验证码添加
     *
     * @param phone 传入存储手机号
     * @return 返回验证码
     */
    public synchronized String addSmsCode(String phone, int type){

        this.checkSendSmsCodeStatus(phone);
        // 添加验证码
        String code = RandomUtils.RandomNumber(6);
        SmsCode smsCode = (SmsCode) new SmsCode()
                .setType(type)
                .setPhone(phone)
                .setCode(code)
                .setExpirationTime(LocalDateTime.now().plusMinutes(5))
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());
        baseMapper.insert(smsCode);
        return code;
    }

    /**
     * 检查验证码是否有效
     *
     * @param phone 手机号
     * @param type  场景
     * @param code  验证码
     * @return 是否正确
     */
    public boolean checkCode(String phone, Integer type, String code) {
        // 使用有效的验证码
        int updateLine = baseMapper.update(new LambdaUpdateWrapper<SmsCode>()
                .eq(SmsCode::getPhone, phone)
                .eq(SmsCode::getType, type)
                .ge(SmsCode::getExpirationTime, LocalDateTime.now())
                .eq(SmsCode::getCode, code)
                .set(SmsCode::getExpirationTime, LocalDateTimeUtil.getDeletedTime()));
        // 判断修改行数
        return updateLine != 0;
    }

}
