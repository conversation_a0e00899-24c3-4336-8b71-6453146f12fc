package com.suakitsu.pre.aspect;


import com.suakitsu.entity.User;
import com.suakitsu.service.Impl.UserCookieServiceImpl;
import com.suakitsu.service.Impl.UserServiceImpl;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 注解 UserLogin 的切面处理
 */
@Aspect
@Component
public class UserLoginAspect {
    @Resource
    UserCookieServiceImpl userCookieService;

    @Resource
    UserServiceImpl userService;

    @Around("@annotation(com.suakitsu.pre.annotation.UserLogin)")
    public Object beforeMethodCheckUserLogin(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取用户信息
        Long userId = userCookieService.getUserInfo();
        User userInfo = userService.getById(userId);
        Object[] args = UserInfoUtils.input(joinPoint, userInfo);
        return joinPoint.proceed(args);
    }
}