package com.suakitsu.model.DailyModel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class DailyReq {
    /**
     * 日记ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 主题
     */
    private String title;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 图片
     */
    private String image;
}