package com.suakitsu.thirdapi.model;

import lombok.Data;

@Data
public class DingTalkModel {
    /**
     * 机器人发送消息
     */
    public static class BotSendMessage {
        @Data
        public static class RequestBody {
            /**
             * 消息类型（文本text）
             */
            private String msgtype;
            /**
             * 消息信息
             */
            private Text text;

            @Data
            public static class Text {
                /**
                 * 消息内容
                 */
                private String content;
            }
        }

        @Data
        public static class RequestParams {
            /**
             * 请求token
             */
            private String access_token;
        }

        @Data
        public static class ResponseBody {
            /**
             * 错误代码（0成功）
             */
            private Integer errcode;
            /**
             * 错误信息
             */
            private String errmsg;
        }
    }
}