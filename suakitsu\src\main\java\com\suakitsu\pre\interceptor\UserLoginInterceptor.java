package com.suakitsu.pre.interceptor;

import com.suakitsu.commonexception.UserLoginException;
import com.suakitsu.pre.annotation.UserLogin;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class UserLoginInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }

        // 判断是否有UserLogin注解
        UserLogin userLoginAnnotation = handlerMethod.getMethodAnnotation(UserLogin.class);

        // 如果没有UserLogin注解，直接通过（改变逻辑）
        if (userLoginAnnotation == null) {
            return true;
        }

        // 有@UserLogin注解的接口需要验证登录
        // 这里添加你的登录验证逻辑
        String authorization = getAuthorizationFromRequest(request);
        if (authorization == null || authorization.isEmpty()) {
            throw new UserLoginException();
        }

        // 验证token有效性
        // if (!isValidToken(authorization)) {
        //     throw new UserLoginException();
        // }

        return true;
    }

    private String getAuthorizationFromRequest(HttpServletRequest request) {
        // 先从Header获取
        String auth = request.getHeader("Authorization");
        if (auth != null && !auth.isEmpty()) {
            return auth;
        }

        // 再从Cookie获取
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("Authorization".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }

        return null;
    }
}