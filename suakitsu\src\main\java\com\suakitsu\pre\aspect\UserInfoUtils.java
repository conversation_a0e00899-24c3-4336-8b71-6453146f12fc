package com.suakitsu.pre.aspect;



import com.suakitsu.entity.User;
import org.aspectj.lang.reflect.MethodSignature;
import org.aspectj.lang.ProceedingJoinPoint;

import java.lang.reflect.Parameter;
import java.util.Objects;
import java.util.stream.IntStream;

public class UserInfoUtils {
    /**
     * 注入用户信息
     *
     * @param joinPoint 方法对象
     * @param userInfo  用户信息
     * @return 方法注入新参数
     */
    public static Object[] input(ProceedingJoinPoint joinPoint, User userInfo) {
        // 取到参数名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();
        // 修改参数名为 userInfo 的参数
        Object[] args = joinPoint.getArgs();
        IntStream.range(0, args.length)
            .filter(i -> Objects.equals(parameters[i].getName(), "userInfo"))
            .forEach(i -> args[i] = userInfo);
        return args;
    }
}
