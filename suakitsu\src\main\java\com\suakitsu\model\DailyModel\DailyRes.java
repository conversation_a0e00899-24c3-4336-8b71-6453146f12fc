package com.suakitsu.model.DailyModel;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class DailyRes {
    /**
     * 日记ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 主题
     */
    private String title;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 图片
     */
    private String image;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}