package com.suakitsu.thirdapi.service;

import com.suakitsu.thirdapi.model.BotSendMessagePar;
import com.suakitsu.thirdapi.model.BotSendMessageReq;
import com.suakitsu.thirdapi.model.BotSendMessageRes;
import com.suakitsu.thirdapi.model.DingTalkModel;
import com.suakitsu.thirdapi.WebClientComponent;
import com.suakitsu.tools.BeanMapUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class DingTalkService {
    @Resource
    WebClientComponent webClientComponent;

    /**
     * 钉钉机器人发送消息
     *
     * @param msg 消息内容
     * @return 是否发送成功
     */
    public boolean botSendMessage(String msg) {
        // 设置查询参数
        BotSendMessagePar requestParams = new BotSendMessagePar()
                .setAccess_token("4a6783f1afa1515eb764d17cfcf9f6c82ea3aeccd48f42bd55ec3939a2b815df");

        // 设置请求参数
        BotSendMessageReq requestBody = new BotSendMessageReq()
                .setMsgtype("text")
                .setText(new BotSendMessageReq.Text()
                        .setContent(msg));

        BotSendMessageRes responseBody = BeanMapUtil.mapToBean(
                webClientComponent.requests(WebClientComponent.Method.POST,
                        "https://oapi.dingtalk.com", "/robot/send", requestParams,
                        requestBody, WebClientComponent.BodyType.JSON).json(),
                BotSendMessageRes.class
        );

        if (responseBody != null) {
            return responseBody.getErrcode() == 0;
        } else {
            return false;
        }
    }
    /**
     * 钉钉机器人发送验证码
     *
     * @param phone 手机号
     * @param code  验证码
     * @return 是否发送成功
     */
    public boolean botSendCode(String phone, String code) {
        return botSendMessage(String.format("""
            【登录测试验证码】
            手机号：%s
            验证码：%s
            """, phone, code));
    }
}