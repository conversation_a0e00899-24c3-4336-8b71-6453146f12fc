package com.suakitsu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.suakitsu.entity.Recommend;
import com.suakitsu.model.RecommendModel.RecommendReq;
import com.suakitsu.model.RecommendModel.RecommendRes;
import com.suakitsu.pre.annotation.UserLogin;
import com.suakitsu.service.Impl.RecommendServiceImpl;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/recommend")
@CrossOrigin
public class RecommendController {

    @Resource
    private RecommendServiceImpl recommendService;

    /**
     * 获取推荐列表
     */
    @GetMapping
    @UserLogin
    @CrossOrigin
    public CommonResult<IPage<RecommendRes>> getRecommendPage(RecommendReq req) {
        IPage<RecommendRes> pageResult = recommendService.getRecommendPage(req);
        return CommonResult.webSuccess(pageResult); // 直接返回分页结果（包含数据和分页信息）
    }

    /**
     * 新增推荐
     */
    @PostMapping
    @CrossOrigin
    @UserLogin
    public CommonResult<Object> add(@RequestBody Recommend recommend) {

        Object result = recommendService.addRecommend(
                recommend.getUserId(),
                recommend.getTitle(),
                recommend.getContent(),
                recommend.getImg()
        );
        if (result != null) {
            return CommonResult.webSuccess("推荐新增成功");
        } else {
            return CommonResult.webError(-1,"推荐新增失败");
        }
    }

    /**
     * 修改推荐
     */
    @PutMapping
    @UserLogin
    @CrossOrigin
    public CommonResult<Object> update(@RequestBody Recommend recommend) {
        boolean isSuccess = recommendService.updateRecommend(recommend);
        if (isSuccess) {
            return CommonResult.webSuccess("推荐修改成功");
        } else {
            return CommonResult.webError(-1,"推荐修改失败");
        }
    }

    /**
     * 删除推荐
     */
    @DeleteMapping("/{id}")
    @UserLogin
    @CrossOrigin
    public CommonResult<Object> delete(@PathVariable Long id) {
        boolean isSuccess = recommendService.deleteRecommend(id);
        if (isSuccess) {
            return CommonResult.webSuccess("推荐删除成功");
        } else {
            return CommonResult.webError(-1,"推荐删除失败");
        }
    }
}

