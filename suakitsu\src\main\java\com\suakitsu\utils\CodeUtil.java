package com.suakitsu.utils;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.File;

public class CodeUtil {
    public void QRCode() {
        // 二维码设置
        QrConfig config = new QrConfig();
        // 纠错级别(越高像素越高，遮挡部分能被识别)
        config.setErrorCorrection(ErrorCorrectionLevel.L);
        //config.setBackColor(Color.black);
        //config.setWidth(500);
        //config.setHeight(500);


        QrCodeUtil.generate("http://www.itcast.cn", 500, 500, new File("D://qr.jpg"));
    }
}
