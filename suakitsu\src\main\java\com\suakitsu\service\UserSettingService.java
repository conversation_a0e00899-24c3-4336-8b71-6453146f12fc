package com.suakitsu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.suakitsu.entity.UserSetting;

/**
 * 用户设置服务接口
 */
public interface UserSettingService extends IService<UserSetting> {
    
    /**
     * 获取用户设置
     * @param userId 用户ID
     * @return 用户设置信息
     */
    UserSetting getUserSetting(Long userId);
    
    /**
     * 创建或更新用户设置
     * @param userSetting 用户设置信息
     * @return 是否成功
     */
    boolean saveOrUpdateUserSetting(UserSetting userSetting);
    
    /**
     * 删除用户设置
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteUserSetting(Long userId);
}