
.container {
  padding: 0.625rem;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.form-container {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.form-header {
  margin-bottom: 0.9375rem;
  text-align: center;
}
.form-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.input-field {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.625rem;
  margin-bottom: 0.625rem;
  border: 0.0625rem solid #eee;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.textarea-field {
  width: 100%;
  height: 6.25rem;
  padding: 0.625rem;
  margin-bottom: 0.625rem;
  border: 0.0625rem solid #eee;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.image-upload {
  margin: 0.625rem 0;
}
.preview-image {
  width: 100%;
  height: 12.5rem;
  border-radius: 0.25rem;
}
.upload-placeholder {
  width: 100%;
  height: 6.25rem;
  border: 0.0625rem dashed #ddd;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}
.icon-camera {
  font-size: 1.5rem;
  margin-bottom: 0.3125rem;
}
.form-actions {
  display: flex;
  gap: 0.625rem;
  margin-top: 0.9375rem;
}
.submit-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  background-color: #4CAF50;
  color: white;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  text-align: center;
  border: none;
}
.cancel-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  text-align: center;
  border: none;
}
.test-btn {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  background-color: #007AFF;
  color: white;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  text-align: center;
  border: none;
}
