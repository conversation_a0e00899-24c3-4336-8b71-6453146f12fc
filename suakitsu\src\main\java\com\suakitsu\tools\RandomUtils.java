package com.suakitsu.tools;

import java.util.Random;

public class RandomUtils {
    /**
     * 生成随机数字
     * @param length 生成的长度
     * @return 返回随机数字
     */
    public static String RandomNumber(int length) {
        Random random = new Random();
        String result = "";
        for (int i = 0; i < length; i++) {
            result += random.nextInt(10);

        }
        return result;
    }
    /**
     * 根据给定的概率生成布尔值
     *
     * @param probability 概率值，范围在0.00到1.00之间
     * @return 如果随机数小于等于给定的概率，则返回true；否则返回false
     */
    public static boolean randomProbability(double probability) {
        // 检查概率值是否在有效范围内
        if (probability < 0.00 || probability > 1.00) {
            throw new IllegalArgumentException("概率值必须在0.00到1.00之间");
        }
        // 生成一个0到1之间的随机数
        Random random = new Random();
        double randomValue = random.nextDouble();

        System.out.println("随机数: " + randomValue + " 概率: " + probability);

        // 根据概率值决定返回结果
        return randomValue <= probability;
    }
    /**
     * 生成随机字符串
     * @param length 生成的长度
     * @return 返回随机字符串
     */
    public static String RandomString(int length) {
        Random random=new Random();
        StringBuffer sb=new StringBuffer();
        for(int i=0;i<length;i++){
            int number=random.nextInt(3);
            long result=0;
            switch(number){
                case 0:
                    result=Math.round(Math.random()*25+65);
                    sb.append(String.valueOf((char)result));
                    break;
                case 1:
                    result=Math.round(Math.random()*25+97);
                    sb.append(String.valueOf((char)result));
                    break;
                case 2:
                    sb.append(String.valueOf(new Random().nextInt(10)));
                    break;
            }

        }
        return sb.toString();
    }
    /**
     * 判断字符串是否为空或null
     */
    public static boolean isBlank(String checkStr) {
        return checkStr == null || checkStr.isEmpty();
    }

    /**
     * 判断字符串是否不为空和null
     */
    public static boolean isNotBlank(String checkStr) {
        return !isBlank(checkStr);
    }

    /**
     * 截取首部某字符串
     */
    public static String stripBefore(String origStr, String stripStr) {
        if (isNotBlank(stripStr) && origStr.startsWith(stripStr)) {
            return stripBefore(origStr.substring(stripStr.length()), stripStr);
        } else {
            return origStr;
        }
    }
}
