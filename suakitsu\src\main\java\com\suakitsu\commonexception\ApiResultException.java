package com.suakitsu.commonexception;

import lombok.Data;

@Data
public class ApiResultException extends RuntimeException {
    private Integer code;
    private String message;

    public ApiResultException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ApiResultException(String message) {
        throw new ApiResultException(-1, message);
    }
}