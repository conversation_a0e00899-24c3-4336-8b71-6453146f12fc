// const BASE_URL = "http://**************:8080/";

// const BASE_URL = "http://localhost:8080/";

const BASE_URL = "http://************:8080/";

/**
 * 请求配置
 */
const config = {
  baseURL: BASE_URL,
  timeout: 10000, // 请求超时时间（毫秒）
  header: {
    'Content-Type': 'application/json'
  }
};

/**
 * 请求拦截器
 * @param {Object} options - 请求配置
 * @returns {Object} - 处理后的请求配置
 */
const requestInterceptor = (options) => {
  // 合并默认配置
  options.url = (options.baseURL || config.baseURL) + (options.url || '');
  options.header = { ...config.header, ...(options.header || {}) };
  options.timeout = options.timeout || config.timeout;

  // 获取并添加token
  const authorization = uni.getStorageSync('authorization');
  if (authorization) {
    options.header['Authorization'] = authorization;
  }

  // 打印请求信息
  console.log('请求拦截器 ==>', {
    url: options.url,
    method: options.method,
    data: options.data,
    header: options.header
  });

  return options;
};

/**
 * 响应拦截器
 * @param {Object} response - 响应数据
 * @param {Object} options - 请求配置
 * @returns {Promise} - 处理后的响应数据
 */
const responseInterceptor = (response, options) => {
  // 打印响应信息
  console.log('响应拦截器 ==>', {
    url: options.url,
    data: response,
    statusCode: response.statusCode
  });

  // 处理HTTP状态码
  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 成功状态
    return Promise.resolve(response.data);
  } else if (response.statusCode === 401) {
    // 未授权，清除token并跳转到登录页
    uni.removeStorageSync('authorization');
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });
    uni.reLaunch({
      url: '/pages/login/login'
    });
    return Promise.reject(response);
  } else {
    // 其他错误状态
    uni.showToast({
      title: response.data?.message || `请求失败(${response.statusCode})`,
      icon: 'none'
    });
    return Promise.reject(response);
  }
};

/**
 * 错误处理
 * @param {Object} error - 错误信息
 * @param {Object} options - 请求配置
 * @returns {Promise} - 处理后的错误信息
 */
const errorHandler = (error, options) => {
  console.error('请求错误 ==>', {
    url: options.url,
    error
  });

  uni.showToast({
    title: '网络异常，请稍后重试',
    icon: 'none'
  });

  return Promise.reject(error);
};

/**
 * 封装请求方法
 * @param {Object} options - 请求配置
 * @returns {Promise} - 请求Promise
 */
const request = (options = {}) => {
  // 应用请求拦截器
  options = requestInterceptor(options);

  // 发起请求
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      success: (res) => {
        // 应用响应拦截器
        responseInterceptor(res, options)
          .then(resolve)
          .catch(reject);
      },
      fail: (err) => {
        // 应用错误处理
        errorHandler(err, options)
          .catch(reject);
      }
    });
  });
};

/**
 * 封装常用请求方法
 */
const http = {
  get: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: 'GET',
      ...options
    });
  },
  post: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: 'POST',
      ...options
    });
  },
  put: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: 'PUT',
      ...options
    });
  },
  delete: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: 'DELETE',
      ...options
    });
  },
  upload: (url, filePath, formData = {}, options = {}) => {
    const uploadOptions = {
      url: (options.baseURL || config.baseURL) + url,
      filePath,
      name: options.name || 'file',
      formData,
      header: options.header || {}
    };

    // 获取并添加token
    const authorization = uni.getStorageSync('authorization');
    if (authorization) {
      uploadOptions.header['Authorization'] = authorization;
    }

    return new Promise((resolve, reject) => {
      uni.uploadFile({
        ...uploadOptions,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 上传成功，尝试解析返回的数据
            try {
              const data = JSON.parse(res.data);
              resolve(data);
            } catch (e) {
              resolve(res.data);
            }
          } else {
            reject(res);
          }
        },
        fail: reject
      });
    });
  }
};

export default {
  BASE_URL,
  config,
  request,
  http
};