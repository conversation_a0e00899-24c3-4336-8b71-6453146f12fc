package com.suakitsu.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suakitsu.mapper.NotesMapper;
import com.suakitsu.entity.Notes;
import com.suakitsu.service.NotesService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Service
public class NotesServiceImpl extends ServiceImpl<NotesMapper, Notes> implements NotesService {

    @Resource
    private NotesMapper notesMapper;

    // 获取待办
    public Object getNotes(Long userId) {
        return notesMapper.selectList(new LambdaQueryWrapper<Notes>()
                .eq(Notes::getUserId, userId));
    }

    public Object addNotes(Long userId, String title, String content) {
        if (title == null || content == null) {
            return false;
        }


        Notes notesModel = new Notes();
        notesModel.setTitle(title).setContent(content).setUserId(userId).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now());

        return baseMapper.insert(notesModel) >=1 ;

    }

    public boolean deleteNotes(Long id, Long userId) {
        if (userId == null || id == null) {
            return false;
        }

        Notes notesModel = new Notes();
        notesModel.setId(id);

        return notesMapper.deleteById(notesModel) >=1 ;
    }

    public boolean updateNotes(Long id, String title, String content, Long userId) {
        if (id == null || userId == null) {
            return false;
        }

        Notes notesModel = notesMapper.selectOne(new LambdaQueryWrapper<Notes>().eq(Notes::getId, id));

        if(notesModel == null){
            return false;
        }
        LocalDateTime timeNow = LocalDateTime.now();


        notesModel.setContent(content).setTitle(title).setUpdateTime(timeNow);
        return notesMapper.updateById(notesModel) >=1 ;
    }
}
