package com.suakitsu.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.suakitsu.model.UserModel.RandomAvatarRes;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Random;

public class RandomUserInformation {

    public static String randomUserAvatar() {
        // 头像链接
        String selectedAvatarUrl = getString();
        String avatarUrl = "";
        // 获取随机头像
        try {
            URL url = new URL(selectedAvatarUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;

                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                // 解析 JSON 数据
                String json = response.toString();
                ObjectMapper objectMapper = new ObjectMapper();
                RandomAvatarRes avatarResponse = objectMapper.readValue(json, RandomAvatarRes.class);
                avatarUrl = avatarResponse.getImgUrl();
            }
        } catch (IOException ignore) {
        }
        return avatarUrl;
    }

    /**
     * 头像json地址
     * @return 返回头像地址
     */
    @NotNull
    private static String getString() {
        String[] avatarUrls = {
            "https://api.uomg.com/api/rand.avatar?sort=男&format=json",
            "https://api.uomg.com/api/rand.avatar?sort=女&format=json",
            "https://api.uomg.com/api/rand.avatar?sort=动漫男&format=json",
            "https://api.uomg.com/api/rand.avatar?sort=动漫女&format=json"
        };
        Random randomImg = new Random();
        int randomIndex = randomImg.nextInt(avatarUrls.length);
        return avatarUrls[randomIndex];
    }

}
