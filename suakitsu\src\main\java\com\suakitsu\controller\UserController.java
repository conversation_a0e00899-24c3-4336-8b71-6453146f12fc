package com.suakitsu.controller;

import com.suakitsu.commonexception.ApiResultException;
import com.suakitsu.config.AppConfig;
import com.suakitsu.entity.SmsCode;
import com.suakitsu.entity.User;
import com.suakitsu.model.UserModel.UserInfoReq;
import com.suakitsu.model.UserModel.UserInfoRes;
import com.suakitsu.model.UserModel.UserLoginReq;
import com.suakitsu.model.UserModel.UserLoginRes;
import com.suakitsu.pre.annotation.UserLogin;
import com.suakitsu.service.Impl.SmsCodeServiceImpl;
import com.suakitsu.service.Impl.UserCookieServiceImpl;
import com.suakitsu.service.Impl.UserServiceImpl;
import com.suakitsu.thirdapi.service.DingTalkService;
import com.suakitsu.thirdapi.service.AliyunService;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

@RestController
@RequestMapping("/user")
@CrossOrigin
public class UserController {
    @Resource
    UserServiceImpl userService;

    @Resource
    SmsCodeServiceImpl smsCodeService;

    @Resource
    UserCookieServiceImpl userCookieService;

    @Resource
    AliyunService aliyunService;


    @Resource
    DingTalkService dingtalkService;

    @Resource
    AppConfig appConfig;

    /**
     * 统一登录接口 - 发送验证码和验证登录
     */
    @PostMapping
    @CrossOrigin
    public CommonResult<UserLoginRes> login(@RequestBody UserLoginReq requestBody) {
        // 如果没有验证码，则发送验证码
        if (requestBody.getCode() == null || requestBody.getCode().isEmpty()) {
            String smsCode = smsCodeService.addSmsCode(requestBody.getPhone(), SmsCode.Type.Login.ordinal());
            
            // 发送验证码
            boolean smsSendResult = switch (appConfig.getSmsConfig().getPlat()) {
                case 1 -> dingtalkService.botSendCode(requestBody.getPhone(), smsCode);
                default -> false;
            };

            if (!smsSendResult) {
                throw new ApiResultException("验证码发送失败，请稍后重试");
            }
            
            return CommonResult.webSuccess("验证码已发送", new UserLoginRes());
        }
        
        // 有验证码则进行验证和登录
        if (!smsCodeService.checkCode(requestBody.getPhone(), SmsCode.Type.Login.ordinal(), requestBody.getCode())) {
            throw new ApiResultException("登录失败，验证码错误");
        }
        
        // 登录账号
        String authorization = userCookieService.addCookie(userService.getUserIdByPhone(requestBody.getPhone()), requestBody.isRemember());
        UserLoginRes responseBody = new UserLoginRes()
                .setAuthorization(authorization);
        return CommonResult.webSuccess(responseBody);
    }

    /**
     * 获取用户信息
     */
    @GetMapping
    @UserLogin
    @CrossOrigin
    public CommonResult<UserInfoRes> info(User userInfo) {
        UserInfoRes responseBody = new UserInfoRes();
        BeanUtils.copyProperties(userInfo, responseBody);

        responseBody.setAvatar(aliyunService.loadFileUrl((userInfo.getAvatar())));

        responseBody.setId(userInfo.getId());
        responseBody.setNickname(userInfo.getNickname());
        responseBody.setGender(userInfo.getGender());
        responseBody.setBirthday(userInfo.getBirthday());

        return CommonResult.webSuccess(responseBody);
    }

    /**
     * 退出登录
     */
    @DeleteMapping
    @UserLogin
    @CrossOrigin
    public CommonResult<Object> logout() {
        userCookieService.deleteCookie();
        return CommonResult.webSuccess("已退出登录");
    }

    /**
     * 上传头像文件
     */
    @PostMapping("/upload-avatar")
    @UserLogin
    @CrossOrigin
    public CommonResult<String> uploadAvatar(@RequestParam("file") MultipartFile file, User userInfo) {
        try {
            System.out.println("收到头像上传请求，用户ID: " + userInfo.getId());
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());

            // 1. 文件校验
            if (file.isEmpty()) {
                return CommonResult.webError(-1, "文件不能为空");
            }

            // 2. 文件类型校验
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isImageFile(originalFilename)) {
                return CommonResult.webError(-1, "只支持图片格式文件");
            }

            // 3. 文件大小校验（限制5MB）
            if (file.getSize() > 5 * 1024 * 1024) {
                return CommonResult.webError(-1, "文件大小不能超过5MB");
            }

            // 4. 生成文件路径：avatar/用户ID/时间戳.扩展名
            String fileExtension = getFileExtension(originalFilename);
            String fileName = userInfo.getId() + "/" + System.currentTimeMillis() + "." + fileExtension;

            // 5. 上传到OSS
            boolean uploadResult = aliyunService.uploadAvatarFile(file, fileName);

            if (uploadResult) {
                // 返回包含 avatar/ 前缀的文件路径
                String filePath = "avatar/" + fileName;
                System.out.println("头像上传成功，返回路径: " + filePath);
                return CommonResult.webSuccess("上传成功", filePath);
            } else {
                return CommonResult.webError(-1, "上传失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.webError(-1, "上传失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为图片文件
     */
    private boolean isImageFile(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        return extension.equals("jpg") || extension.equals("jpeg") ||
                extension.equals("png") || extension.equals("gif") ||
                extension.equals("bmp") || extension.equals("webp");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "jpg"; // 默认扩展名
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }
    /**
     * 修改用户信息
     */
    @PutMapping
    @UserLogin
    @CrossOrigin
    public CommonResult<Object> editUserInfo(@RequestBody UserInfoReq requestBody, User userInfo) {
        try {
            System.out.println("=== 修改用户信息 ===");
            System.out.println("用户ID: " + userInfo.getId());
            System.out.println("请求数据: " + requestBody);

            userService.editUserInfo(userInfo.getId(),
                    requestBody.getNickname(),
                    requestBody.getAvatar(), // 直接使用传入的头像路径
                    requestBody.getGender(),
                    requestBody.getBirthday());

            return CommonResult.webSuccess("修改成功");
        } catch (Exception e) {
            System.err.println("用户信息更新失败: " + e.getMessage());
            e.printStackTrace();
            return CommonResult.webError(-1, "用户信息更新失败：" + e.getMessage());
        }
    }
}