package com.suakitsu.controller;


import com.suakitsu.thirdapi.service.AliyunService;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
public class AliyunUploadController {


    @Resource
    private AliyunService aliyunService;

    //依赖注入
    @PostMapping("/tengxun")
    public CommonResult<Boolean> upload(MultipartFile file, String fileName) {
            return CommonResult.webSuccess(aliyunService.uploadFile(file, fileName));
    }
}