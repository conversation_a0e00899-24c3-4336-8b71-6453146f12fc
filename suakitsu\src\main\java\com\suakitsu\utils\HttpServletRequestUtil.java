package com.suakitsu.utils;

import jakarta.servlet.http.HttpServletRequest;

public class HttpServletRequestUtil {
    public static String getRemoteAddr(HttpServletRequest request) {
        String[] headerNames = {"x-forwarded-for", "Proxy-Client-IP", "WL-Proxy-Client-IP"};
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
        }
        return "unknown";
    }
}
