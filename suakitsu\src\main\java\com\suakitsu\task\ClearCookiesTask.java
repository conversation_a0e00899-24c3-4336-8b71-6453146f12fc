package com.suakitsu.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.suakitsu.entity.UserCookie;
import com.suakitsu.mapper.UserCookieMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.LifecycleState;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ClearCookiesTask {

    @Resource
    private UserCookieMapper userCookieMapper;

    @Scheduled(cron = "0 0 0 ? * ?")
    public void clearCookies() {

        // 查询所有 UserCookie 记录，按 userId 分组
        List<UserCookie> userCookieList = userCookieMapper.selectList(new LambdaQueryWrapper<UserCookie>()
                .isNotNull(UserCookie::getUserId)); // 排除没有 userId 的记录

        if (userCookieList != null && !userCookieList.isEmpty()) {

            // 按 userId 分组
            Map<Long, List<UserCookie>> userCookiesByUserId = userCookieList.stream()
                    .collect(Collectors.groupingBy(UserCookie::getUserId));

            // 遍历每个 userId 下的记录
            for (Map.Entry<Long, List<UserCookie>> entry : userCookiesByUserId.entrySet()) {
                Long userId = entry.getKey();  // 获取当前的 userId
                List<UserCookie> cookies = entry.getValue();  // 获取该 userId 对应的 cookies

                // 如果该 userId 下有多条记录，处理删除
                if (cookies.size() > 1) {
                    // 按 expirationTime 排序，降序，确保最新的记录排在前面
                    cookies.sort(Comparator.comparing(UserCookie::getExpirationTime).reversed());

                    // 获取除了最新一条外的记录ID
                    List<Long> idsToDelete = cookies.stream()
                            .skip(1)  // 跳过最新的记录
                            .map(UserCookie::getId)
                            .collect(Collectors.toList());

                    // 删除这些记录
                    if (!idsToDelete.isEmpty()) {
                        // 使用 userId 进行日志记录
                        log.info("清理 userId={} 的过期 cookies，删除记录：{}", userId, idsToDelete);

                        // 删除这些记录
                        userCookieMapper.deleteByIds(idsToDelete);
                    }
                }
            }
        }
    }



}
