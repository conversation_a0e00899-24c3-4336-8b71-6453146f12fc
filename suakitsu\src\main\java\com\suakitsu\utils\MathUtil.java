package com.suakitsu.utils;

public class MathUtil {
    /**
     * 获取随机数
     *
     * @param min 最小值
     * @param max 最大值
     * @return 结果
     */
    public static Double random(Double min, Double max) {
        return min + Math.random() * (max - min);
    }

    public static Float random(Float min, Float max) {
        return (float) (min + Math.random() * (max - min));
    }

    public static Integer random(Integer min, Integer max) {
        return Math.toIntExact(Math.round(min + Math.random() * (max - min)));
    }
}