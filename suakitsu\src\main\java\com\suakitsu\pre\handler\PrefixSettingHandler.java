package com.suakitsu.pre.handler;

import lombok.NonNull;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;

public class PrefixSettingHandler extends RequestMappingHandlerMapping {
    /**
     * 自动修改控制器路径前缀
     */
    @Override
    protected RequestMappingInfo getMappingForMethod(@NonNull Method method, @NonNull Class<?> handlerType) {
        RequestMappingInfo mappingForMethod = super.getMappingForMethod(method, handlerType);
        String prefix = getPrefix(handlerType);
        if (mappingForMethod != null) {
            return RequestMappingInfo.paths(prefix).build().combine(mappingForMethod);
        } else {
            return null;
        }
    }

    /**
     * 定义路径前缀为 controller 目录开始后的子目录
     */
    private String getPrefix(Class<?> handlerType) {
        String packageName = handlerType.getPackage().getName();

        // 查找到根包并替换路径
        String ROOT_PACKAGE_NAME = "controller";
        int packageIndex = packageName.indexOf(ROOT_PACKAGE_NAME);
        String doPath = packageName.substring(packageIndex + ROOT_PACKAGE_NAME.length());
        return doPath.replace('.', '/');
    }
}
