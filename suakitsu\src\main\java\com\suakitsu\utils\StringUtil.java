package com.suakitsu.utils;

/**
 * 字符串工具类
 */
public class StringUtil {
    /**
     * 随机生成字符串
     *
     * @param charList 字符列表
     * @param length   长度
     * @return 生成的字符串
     */
    public static String random(String charList, Integer length) {
        StringBuilder stringBuilder = new StringBuilder();
        int charLength = charList.length();
        for (int i = 0; i < length; i++) {
            int number = MathUtil.random(0, charLength - 1);
            stringBuilder.append(charList.charAt(number));
        }
        return stringBuilder.toString();
    }

    /**
     * 随机生成字符串 从（0-9a-zA-Z）随机
     *
     * @param length 长度
     * @return 生成的字符串
     */
    public static String random(Integer length) {
        return random("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", length);
    }

    /**
     * 判断字符串是否为空或null
     */
    public static boolean isBlank(String checkStr) {
        return checkStr == null || checkStr.isEmpty();
    }

    /**
     * 判断字符串是否不为空和null
     */
    public static boolean isNotBlank(String checkStr) {
        return !isBlank(checkStr);
    }

    /**
     * 截取首部某字符串
     */
    public static String stripBefore(String origStr, String stripStr) {
        if (isNotBlank(stripStr) && origStr.startsWith(stripStr)) {
            return stripBefore(origStr.substring(stripStr.length()), stripStr);
        } else {
            return origStr;
        }
    }
}
