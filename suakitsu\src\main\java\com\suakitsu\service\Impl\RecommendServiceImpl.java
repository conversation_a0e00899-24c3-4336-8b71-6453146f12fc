package com.suakitsu.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suakitsu.entity.Daily;
import com.suakitsu.entity.Recommend;
import com.suakitsu.mapper.RecommendMapper;
import com.suakitsu.model.RecommendModel.RecommendReq;
import com.suakitsu.model.RecommendModel.RecommendRes;
import com.suakitsu.service.RecommendService;
import com.suakitsu.thirdapi.service.AliyunService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class RecommendServiceImpl extends ServiceImpl<RecommendMapper, Recommend> implements RecommendService {

    @Resource
    private RecommendMapper recommendMapper;
    @Resource
    private AliyunService aliyunService;
    @Resource
    private UserServiceImpl userService;

    /**
     * 查询推荐列表
     */
    public IPage<RecommendRes> getRecommendPage(RecommendReq req) {
        Page<Recommend> page = new Page<>(req.getPageNum(), req.getPageSize());
        // 执行分页查询
        IPage<Recommend> recommendPage = recommendMapper.selectPage(page, new LambdaQueryWrapper<>());

        // 将查询结果转换为 RecommendRes 列表
        List<RecommendRes> recordsList = recommendPage.getRecords().stream()
                .map(recommend -> {
                    RecommendRes res = new RecommendRes();
                    res.setTitle(recommend.getTitle());
                    res.setContent(recommend.getContent());
                    res.setUserId(recommend.getUserId());
                    res.setImg(aliyunService.loadFileUrl(recommend.getImg()));
                    res.setAvatar(aliyunService.loadFileUrl(userService.getUserInfoById(recommend.getUserId()).getAvatar()));
                    res.setNickname(userService.getUserInfoById(recommend.getUserId()).getNickname());
                    return res;
                })
                .collect(Collectors.toList());

        // 创建并填充包含 RecommendRes 的分页对象
        Page<RecommendRes> recommendResPage = new Page<>();
        recommendResPage.setCurrent(recommendPage.getCurrent());
        recommendResPage.setSize(recommendPage.getSize());
        recommendResPage.setTotal(recommendPage.getTotal());
        recommendResPage.setRecords(recordsList);

        return recommendResPage;
    }

    /**
     * 新增推荐
     */
    public Object addRecommend(Long userId, String title, String content, String image) {
        try {
            System.out.println("=== DailyService.addDaily 调试信息 ===");
            System.out.println("userId: " + userId);
            System.out.println("title: " + title);
            System.out.println("content: " + content);
            System.out.println("image: " + image);

            if (userId == null) {
                System.err.println("错误: userId 为 null");
                return false;
            }

            if (title == null || content == null) {
                System.err.println("错误: title 或 content 为 null");
                return false;
            }

            Recommend recommend = new Recommend();
            recommend.setTitle(title)
                    .setContent(content)
                    .setImg(image)
                    .setUserId(userId)  // 确保设置了 userId
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            System.out.println("准备插入的 Daily 对象: " + recommend);

            int insertResult = baseMapper.insert(recommend);
            System.out.println("插入结果: " + insertResult);

            return insertResult >= 1;

        } catch (Exception e) {
            System.err.println("DailyService.addDaily 异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除推荐
     */
    public boolean deleteRecommend(Long id) {
        // 根据 id 删除推荐
        int rows = recommendMapper.deleteById(id);
        return rows > 0;
    }

    /**
     * 修改推荐
     */
    public boolean updateRecommend(Recommend recommend) {
        // 根据 recommend 的 id 修改数据
        int rows = recommendMapper.updateById(recommend);
        return rows > 0;
    }

}
