package com.suakitsu.controller;

import com.suakitsu.entity.User;
import com.suakitsu.model.UserSettingModel.UserSettingReq;
import com.suakitsu.model.UserSettingModel.UserSettingRes;
import com.suakitsu.pre.annotation.UserLogin;
import com.suakitsu.service.Impl.UserServiceImpl;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 用户设置控制器
 * 用于管理用户的个人设置信息
 */
@RestController
@RequestMapping("/user/setting")
@CrossOrigin
public class UserSettingController {

    @Resource
    private UserServiceImpl userService;

    /**
     * 获取用户设置信息
     * @param userId 用户ID
     * @return 用户设置信息
     */
    @UserLogin
    @GetMapping("/")
    public CommonResult<?> getUserSetting(@RequestAttribute("userId") Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return CommonResult.webError(-1,"用户不存在");
        }
        
        UserSettingRes res = new UserSettingRes();
        BeanUtils.copyProperties(user, res);
        return CommonResult.webSuccess(res);
    }

    /**
     * 创建用户设置信息
     * @param userId 用户ID
     * @param req 用户设置请求
     * @return 操作结果
     */
    @UserLogin
    @PostMapping("/")
    public CommonResult<?> createUserSetting(
            @RequestAttribute("userId") Long userId,
            @RequestBody UserSettingReq req) {
        User user = userService.getById(userId);
        if (user == null) {
            return CommonResult.webError(-1,"用户不存在");
        }
        
        BeanUtils.copyProperties(req, user);
        userService.updateById(user);
        
        UserSettingRes res = new UserSettingRes();
        BeanUtils.copyProperties(user, res);
        return CommonResult.webSuccess(res);
    }

    /**
     * 更新用户设置信息
     * @param userId 用户ID
     * @param req 用户设置请求
     * @return 操作结果
     */
    @UserLogin
    @PutMapping("/")
    public CommonResult<?> updateUserSetting(
            @RequestAttribute("userId") Long userId,
            @RequestBody UserSettingReq req) {
        User user = userService.getById(userId);
        if (user == null) {
            return CommonResult.webError(-1,"用户不存在");
        }
        
        BeanUtils.copyProperties(req, user);
        userService.updateById(user);
        
        UserSettingRes res = new UserSettingRes();
        BeanUtils.copyProperties(user, res);
        return CommonResult.webSuccess(res);
    }

    /**
     * 删除用户设置信息（重置为默认值）
     * @param userId 用户ID
     * @return 操作结果
     */
    @UserLogin
    @DeleteMapping("/")
    public CommonResult<?> deleteUserSetting(@RequestAttribute("userId") Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return CommonResult.webError(-1,"用户不存在");
        }
        
        // 重置用户设置为默认值
        user.setNickname("用户" + user.getPhone().substring(user.getPhone().length() - 4));
        user.setAvatar(null);
        user.setGender(null);
        user.setBirthday(null);
        
        userService.updateById(user);
        return  CommonResult.webSuccess("用户设置已重置");
    }
}