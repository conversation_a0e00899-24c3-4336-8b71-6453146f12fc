package com.suakitsu.thirdapi;

import com.suakitsu.thirdapi.model.SendSmsRes;
import com.suakitsu.utils.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.suakitsu.commonexception.ApiResultException;
import com.suakitsu.tools.BeanMapUtil;
import com.suakitsu.tools.RandomUtils;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Setter;
import lombok.SneakyThrows;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Setter
@Component
public class WebClientComponent {
    /**
     * 请求返回内容
     */
    private String responseContent = "";

    public enum Method {
        GET,
        POST
    }

    public enum BodyType {
        FORM,
        JSON
    }

    @Resource
    HttpServletRequest request;

    /**
     * 发起请求
     */
    public WebClientComponent requests(Method method, String url, String uri, Object params,
                                       Object body, BodyType bodyType) {
        // 添加params参数，记录日志
        HttpUrl.Builder httpBuilder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder()
                .addPathSegments(StringUtil.stripBefore(uri, "/"));
        Map<String, Object> paramsToMap = BeanMapUtil.beanToMap(params);
        if (paramsToMap != null) {
            for (Map.Entry<String, Object> paramsWithOne : paramsToMap.entrySet()) {
                httpBuilder.addQueryParameter(paramsWithOne.getKey(), String.valueOf(paramsWithOne.getValue()));
            }
        }
        String sendUrl = httpBuilder.build().toString();

        // 定义请求
        Request request;
        String[] requestBody = this.getRequestBody(body, bodyType);
        if (method == Method.POST) {
            request = new Request.Builder()
                    .url(sendUrl)
                    .post(RequestBody.create(requestBody[0], MediaType.parse(requestBody[1])))
                    .build();
        } else {
            request = new Request.Builder()
                    .url(sendUrl)
                    .get()
                    .build();
        }

        // 设置请求超时时间
        OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .callTimeout(60, TimeUnit.SECONDS)
                .build();

        // 发起请求并返回
        WebClientComponent webClientUtil = new WebClientComponent();
        try (Response response = okHttpClient.newCall(request).execute()) {
            String responseContent = response.body() != null ? response.body().string() : "{\"msg\": \"BodyError\"}";
            return webClientUtil.setResponseContent(responseContent);
        } catch (IOException ignore) {
            return webClientUtil.setResponseContent("{\"msg\": \"RequestError\"}");
        }
    }

    /**
     * 根据请求参数格式获取请求实际发送数据
     * [0] 请求字符串 [1] 请求格式
     */
    private String[] getRequestBody(Object body, BodyType bodyType) {
        Map<String, Object> bodyToMap = BeanMapUtil.beanToMap(body);
        if (bodyType == BodyType.JSON) {
            // 解析为Json
            try {
                return new String[]{new ObjectMapper().writeValueAsString(bodyToMap), "application/json; charset=utf-8"};
            } catch (JsonProcessingException ignore) {
                return new String[]{"{\"msg\": \"None\"}", "application/json; charset=utf-8"};
            }
        } else {
            // 解析为表单
            StringBuilder stringBuilder = new StringBuilder();
            if (bodyToMap != null) {
                for (Map.Entry<String, Object> bodyWithOne : bodyToMap.entrySet()) {
                    stringBuilder
                            .append(bodyWithOne.getKey())
                            .append("=")
                            .append(URLEncoder.encode(String.valueOf(bodyWithOne.getValue()), StandardCharsets.UTF_8))
                            .append("&");
                }
            }
            return new String[]{stringBuilder.toString(), "application/x-www-form-urlencoded"};
        }
    }

    /**
     * 获取原始文本
     */
    public String text() {
        return this.responseContent;
    }

    /**
     * json解析
     */
    @SneakyThrows
    public <T> T json(Class<T> clazz) {
        // 定义反序列化
        ObjectMapper objectMapper = new ObjectMapper();
        // 设置时间反序列化
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        objectMapper.registerModule(javaTimeModule);
        // 忽略不存在字段
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(this.text(), clazz);
        } catch (JsonProcessingException e) {
            return clazz.getDeclaredConstructor().newInstance();
        }
    }

    /**
     * json默认解析为Map
     */
    public Map<String, Object> json() {
        // 定义反序列化，忽略不存在字段
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        JavaType mapClazz = typeFactory.constructParametricType(Map.class, String.class, Object.class);
        try {
            return objectMapper.readValue(this.text(), mapClazz);
        } catch (JsonProcessingException e) {
            return new HashMap<>();
        }
    }
}