package com.suakitsu.controller;

import com.suakitsu.model.DailyModel.DailyReq;
import com.suakitsu.pre.annotation.UserLogin;
import com.suakitsu.service.Impl.DailyServiceImpl;
import com.suakitsu.service.Impl.RecommendServiceImpl;
import com.suakitsu.thirdapi.service.AliyunService;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;
import java.util.UUID;

@RestController
@RequestMapping("/daily")
@CrossOrigin
public class DailyController {
    @Resource
    private DailyServiceImpl dailyService;
    @Resource
    private AliyunService aliyunService;
    @Resource
    private RecommendServiceImpl recommendService;

    // 查询用户的所有日记
    @GetMapping
    @CrossOrigin
    @UserLogin
    public CommonResult<Object> getDaily(@RequestParam Long userId) {
        if (userId == null) {
            return CommonResult.webError(-1,"用户不存在");
        }
        return CommonResult.webSuccess("获取成功",dailyService.getDaily(userId));
    }

    // 添加日记
    @PostMapping
    @CrossOrigin
    public CommonResult<Object> addDaily(@RequestBody DailyReq dailyReq) {
        try {
            System.out.println("=== 添加日记调试信息 ===");
            System.out.println("接收到的数据: " + dailyReq);

            if (dailyReq == null) {
                return CommonResult.webError(-1, "数据格式错误");
            }

            // 验证必要字段
            if (dailyReq.getUserId() == null) {
                return CommonResult.webError(-1, "用户ID不能为空");
            }

            if (dailyReq.getTitle() == null || dailyReq.getTitle().trim().isEmpty()) {
                return CommonResult.webError(-1, "标题不能为空");
            }

            if (dailyReq.getContent() == null || dailyReq.getContent().trim().isEmpty()) {
                return CommonResult.webError(-1, "内容不能为空");
            }

            System.out.println("用户ID: " + dailyReq.getUserId());
            System.out.println("标题: " + dailyReq.getTitle());
            System.out.println("内容: " + dailyReq.getContent());
            System.out.println("图片: " + dailyReq.getImage());


            Object result = recommendService.addRecommend(
                    dailyReq.getUserId(),
                    dailyReq.getTitle(),
                    dailyReq.getContent(),
                    dailyReq.getImage()
            );

            System.out.println("添加结果: " + result);

            return CommonResult.webSuccess("添加成功", result);

        } catch (Exception e) {
            System.err.println("添加日记异常: " + e.getMessage());
            e.printStackTrace();
            return CommonResult.webError(-1, "添加失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload")
    @CrossOrigin
    public CommonResult<String> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            System.out.println("收到上传请求，文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());

            // 1. 文件校验
            if (file.isEmpty()) {
                return CommonResult.webError(-1, "文件不能为空");
            }

            // 2. 生成唯一文件名
            String fileName = UUID.randomUUID() + "." + getFileExtension(Objects.requireNonNull(file.getOriginalFilename()));

            boolean uploadResult = aliyunService.uploadRecommendFile(file, fileName);

            if (uploadResult) {
                System.out.println("返回的文件路径: " + fileName);
                return CommonResult.webSuccess("上传成功", fileName);
            } else {
                return CommonResult.webError(-1, "上传失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.webError(-1, "上传失败: " + e.getMessage());
        }
    }

    private String getFileExtension(String filename) {
        return filename.substring(filename.lastIndexOf(".") + 1);
    }

    // 修改日记
    @PutMapping
    @CrossOrigin
    public CommonResult<Object> updateDaily(@RequestBody DailyReq dailyReq) {
        if (dailyReq == null) {
            return CommonResult.webError(-1,"数据格式错误");
        }
        return CommonResult.webSuccess("修改成功",dailyService.updateDaily(
            dailyReq.getId(),  
            dailyReq.getTitle(), 
            dailyReq.getContent(),
            dailyReq.getImage(),
            dailyReq.getUserId()
        ));
    }
    
    // 删除日记
    @DeleteMapping
    @CrossOrigin
    @UserLogin
    public CommonResult<Object> deleteDaily(@RequestParam Long id, @RequestParam Long userId) {
        if (id == null || userId == null) {
            return CommonResult.webError(-1,"数据格式错误");
        }
        return CommonResult.webSuccess("删除成功",dailyService.deleteDaily(id, userId));
    }
}