<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="logo-area">
				<image class="logo" src="/static/pet/rabbit.png"></image>
				<text class="title">推荐</text>
				<view class="refresh-icon" @click="handleRefresh">
					<text class="refresh-text">🔄</text>
				</view>
			</view>
			<view class="search-box">
				<input
					class="search-input"
					v-model="searchKeyword"
					placeholder="搜索标题或内容..."
					@input="handleSearch"
					@confirm="handleSearch"
				/>
				<view class="search-icon" v-if="!searchKeyword" @click="focusSearch">
					<text class="iconfont icon-search">🔍</text>
				</view>
				<view class="clear-icon" v-if="searchKeyword" @click="clearSearch">
					<text>✕</text>
				</view>
			</view>
		</view>
		
		<!-- 瀑布流内容区 -->
		<scroll-view
			class="waterfall"
			scroll-y
			@scrolltolower="loadMore"
			@scroll="onScroll"
			:scroll-top="scrollTop"
			lower-threshold="100"
		>
			<view class="waterfall-wrapper">
				<!-- 左列 -->
				<view class="waterfall-column left-column">
					<view 
						class="waterfall-item" 
						v-for="(item, index) in leftList" 
						:key="index" 
						@click="viewDetail(item)"
						:style="{marginBottom: (10 + Math.floor(Math.random() * 15)) + 'rpx'}"
					>
						<!-- 图片/视频内容 -->
						<view class="media-wrapper" :style="{height: getRandomHeight(item)}">
							<image class="item-image" :src="item.img" mode="aspectFill" @click.stop="previewImage(item.img)"></image>
						</view>
						
						<!-- 文字内容 -->
						<view class="item-content">
							<text class="item-title" :style="{'-webkit-line-clamp': index % 2 === 0 ? 2 : 1}">{{item.title}}</text>
							<text class="item-desc" :style="{'-webkit-line-clamp': index % 5 === 0 ? 4 : (index % 3 === 0 ? 3 : 2)}">{{item.content}}</text>
							
							<!-- 底部信息 -->
							<view class="item-footer">
								<view class="user-info">
									<image
										class="user-avatar"
										:src="item.avatar || '/static/user/avatar.jpg'"
										mode="aspectFill"
										@error="handleAvatarError"
									></image>
									<text class="user-name">{{item.nickname}}</text>
								</view>
								<view class="like-info">
									<image class="icon-rabbit" src="/static/pet/rabbit.png"></image>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 右列 -->
				<view class="waterfall-column right-column">
					<view 
						class="waterfall-item" 
						v-for="(item, index) in rightList" 
						:key="index" 
						@click="viewDetail(item)"
						:style="{marginBottom: (10 + Math.floor(Math.random() * 15)) + 'rpx'}"
					>
						<!-- 图片/视频内容 -->
						<view class="media-wrapper" :style="{height: getRandomHeight(item)}">
							<image class="item-image" :src="item.img" mode="aspectFill" @click.stop="previewImage(item.img)"></image>

							<!-- 悬浮标题 -->
							<view class="floating-title" v-if="index % 7 === 0">
								<text>{{item.title}}</text>
							</view>
						</view>
						
						<!-- 文字内容 -->
						<view class="item-content">
							<text class="item-title" :style="{'-webkit-line-clamp': index % 2 === 0 ? 1 : 2}">{{item.title}}</text>
							<text class="item-desc" :style="{'-webkit-line-clamp': index % 4 === 0 ? 3 : 2}">{{item.content}}</text>
							
							<!-- 底部信息 -->
							<view class="item-footer">
								<view class="user-info">
									<image
										class="user-avatar"
										:src="item.avatar || '/static/user/avatar.jpg'"
										mode="aspectFill"
										@error="handleAvatarError"
									></image>
									<text class="user-name">{{item.nickname}}</text>
								</view>
								<view class="like-info">
									<image class="icon-rabbit" src="/static/pet/rabbit.png"></image>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="loading" v-if="isLoading">
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 手动加载更多按钮 -->
			<view class="load-more-btn" v-if="!isLoading && !noMore && !searchKeyword && pageNum < totalPages" @click="loadMoreManually">
				<text class="load-more-text">点击加载更多</text>
			</view>

			<view class="no-more" v-if="noMore && !searchKeyword && !isLoading">
				<text class="no-more-text">没有更多了~</text>
			</view>
		</scroll-view>

		<!-- 回到顶部按钮 -->
		<view class="back-to-top" v-if="showBackToTop" @click="backToTop">
			<text class="back-to-top-icon">↑</text>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref,
		computed
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import request from '../../../api/request';

	const baseURL = request.BASE_URL;
	const recommendList = ref([]);
	const originalList = ref([]); // 存储原始数据，用于搜索
	const pageNum = ref(1);
	const pageSize = ref(10);
	const total = ref(0);
	const totalPages = ref(0);
	const isLoading = ref(false);
	const noMore = ref(false);
	const searchKeyword = ref('');
	const showBackToTop = ref(false);
	const scrollTop = ref(0);
	
	// 生成随机高度，但有限制范围
	const getRandomHeight = (item) => {
		// 基于内容长度和随机因子生成高度
		const contentLength = (item.title?.length || 0) + (item.content?.length || 0);
		const baseHeight = 320 + (contentLength * 0.8); // 基础高度
		const randomFactor = Math.random() * 100 - 50; // 随机浮动±50
		const finalHeight = Math.max(240, Math.min(500, baseHeight + randomFactor)); // 限制最小和最大高度
		return finalHeight + 'rpx';
	};
	
	// 计算左右两列的数据
	const leftList = computed(() => {
		return recommendList.value.filter((_, index) => index % 2 === 0);
	});
	
	const rightList = computed(() => {
		return recommendList.value.filter((_, index) => index % 2 === 1);
	});

	// 获取推荐列表
	const getRecommendList = async (page = 1, refresh = false) => {
		if (isLoading.value && !refresh) return; // 刷新时允许重新加载

		isLoading.value = true;

		try {
			// 获取authorization
			const authorization = uni.getStorageSync('authorization');

			console.log(`=== 获取推荐列表 第${page}页 ===`);
			console.log('是否刷新:', refresh);
			console.log('当前总页数:', totalPages.value);

			// 使用与Python代码相同的方式发送请求
			const res = await uni.request({
				url: baseURL + 'recommend',
				method: 'GET',
				header: {
					'Cookie': `Authorization=${authorization}`
				},
				data: {
					pageNum: page,
					pageSize: pageSize.value
				},
				withCredentials: true  // 允许携带证书（cookies）
			});

			console.log('推荐列表响应:', res);

			if (res.data && res.data.data) {
				const responseData = res.data.data;

				// 更新总记录数和总页数
				total.value = responseData.total;
				totalPages.value = responseData.pages;

				console.log(`数据统计: 总记录${total.value}条, 总页数${totalPages.value}页, 当前第${page}页`);

				// 创建带随机属性的数据 (模拟展示用)
				const processedData = (responseData.records || []).map(item => {
					return {
						...item,
						// 如果内容为空或太短，生成随机内容
						content: item.content || '这是一段随机生成的内容，用来展示瀑布流布局效果。内容长度不同，会影响卡片高度。'
					};
				});

				console.log(`处理后的数据数量: ${processedData.length}条`);

				// 如果是刷新，则替换列表，否则追加
				if (refresh) {
					recommendList.value = processedData;
					originalList.value = processedData; // 同时更新原始数据
					pageNum.value = 1; // 重置页码
					noMore.value = false; // 重置加载完成状态
					console.log('刷新完成，重置列表');
				} else {
					recommendList.value = [...recommendList.value, ...processedData];
					originalList.value = [...originalList.value, ...processedData];
					console.log(`追加数据完成，当前列表总数: ${recommendList.value.length}条`);
				}

				// 判断是否还有更多数据
				noMore.value = page >= totalPages.value;
				console.log('是否还有更多数据:', !noMore.value);
			}
		} catch (error) {
			console.error('获取推荐列表失败:', error);
			uni.showToast({
				title: '加载失败，请重试',
				icon: 'none'
			});
		} finally {
			isLoading.value = false;
		}
	};
	
	// 加载更多（滚动到底部时触发）
	const loadMore = () => {
		if (noMore.value || isLoading.value || searchKeyword.value) return; // 搜索时不加载更多

		console.log(`=== 滚动到底部，触发加载更多 ===`);
		console.log('当前页码:', pageNum.value);
		console.log('总页数:', totalPages.value);

		if (pageNum.value < totalPages.value) {
			pageNum.value++;
			console.log('滚动加载第', pageNum.value, '页');
			getRecommendList(pageNum.value, false);
		} else {
			console.log('已经是最后一页，不再加载');
			noMore.value = true;
		}
	};

	// 手动加载更多
	const loadMoreManually = () => {
		if (noMore.value || isLoading.value || searchKeyword.value) return;

		console.log(`=== 手动点击加载更多 ===`);
		console.log('当前页码:', pageNum.value);
		console.log('总页数:', totalPages.value);

		if (pageNum.value < totalPages.value) {
			pageNum.value++;
			console.log('手动加载第', pageNum.value, '页');
			getRecommendList(pageNum.value, false);
		}
	};

	// 点击刷新
	const handleRefresh = async () => {
		console.log('=== 点击刷新 ===');

		if (isLoading.value) {
			console.log('正在加载中，忽略刷新请求');
			return;
		}

		// 显示刷新提示
		uni.showToast({
			title: '刷新中...',
			icon: 'loading',
			duration: 1000
		});

		searchKeyword.value = ''; // 清空搜索
		pageNum.value = 1; // 重置页码
		noMore.value = false; // 重置加载完成状态

		try {
			await getRecommendList(1, true);
			console.log('刷新完成');
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1000
			});
		} catch (error) {
			console.error('刷新失败:', error);
			uni.showToast({
				title: '刷新失败',
				icon: 'error',
				duration: 1000
			});
		}
	};

	// 搜索功能
	const handleSearch = () => {
		if (!searchKeyword.value.trim()) {
			// 如果搜索关键词为空，显示所有数据
			recommendList.value = originalList.value;
			return;
		}

		const keyword = searchKeyword.value.toLowerCase();
		const filteredList = originalList.value.filter(item => {
			return (item.title && item.title.toLowerCase().includes(keyword)) ||
				   (item.content && item.content.toLowerCase().includes(keyword));
		});

		recommendList.value = filteredList;
	};

	// 清空搜索
	const clearSearch = () => {
		searchKeyword.value = '';
		recommendList.value = originalList.value;
	};

	// 聚焦搜索框
	const focusSearch = () => {
		// 可以添加聚焦逻辑
	};

	// 监听滚动事件
	const onScroll = (e) => {
		const currentScrollTop = e.detail.scrollTop;
		const scrollHeight = e.detail.scrollHeight;
		const scrollViewHeight = e.detail.scrollViewHeight || 0;

		// 当滚动超过 500px 时显示回到顶部按钮
		const shouldShow = currentScrollTop > 500;
		if (shouldShow !== showBackToTop.value) {
			showBackToTop.value = shouldShow;
		}

		// 预加载：当滚动到距离底部200px时开始加载下一页
		if (scrollHeight > 0 && scrollViewHeight > 0) {
			const distanceToBottom = scrollHeight - currentScrollTop - scrollViewHeight;
			if (distanceToBottom < 200 && !isLoading.value && !noMore.value && !searchKeyword.value) {
				console.log('=== 预加载触发 ===');
				console.log('距离底部:', distanceToBottom);
				console.log('当前页码:', pageNum.value);
				console.log('总页数:', totalPages.value);

				if (pageNum.value < totalPages.value) {
					pageNum.value++;
					console.log('预加载第', pageNum.value, '页');
					getRecommendList(pageNum.value, false);
				}
			}
		}
	};

	// 回到顶部
	const backToTop = () => {
		console.log('点击回到顶部');
		// 设置滚动位置为 0
		scrollTop.value = 0;

		// 使用 nextTick 确保滚动生效
		uni.nextTick(() => {
			// 强制触发滚动更新
			scrollTop.value = 1;
			uni.nextTick(() => {
				scrollTop.value = 0;
			});
		});
	};
	
	// 查看详情
	const viewDetail = (item) => {
		uni.navigateTo({
			url: `/pages/recommend/detail?id=${item.id}`
		});
	};

	// 图片预览功能
	const previewImage = (imageUrl) => {
		uni.previewImage({
			urls: [imageUrl],
			current: imageUrl,
			indicator: 'number',
			loop: false
		});
	};

	// 头像加载错误处理
	const handleAvatarError = (e) => {
		console.log('头像加载失败，使用默认头像');
		e.target.src = '/static/user/avatar.jpg';
	};

	onMounted(async () => {
		console.log('=== 页面 mounted ===');
		// 初始化状态
		showBackToTop.value = false;
		scrollTop.value = 0;
		pageNum.value = 1;
		noMore.value = false;

		console.log('初始化分页参数:');
		console.log('pageNum:', pageNum.value);
		console.log('pageSize:', pageSize.value);
		console.log('noMore:', noMore.value);

		await getRecommendList(1, true);
	});

	onLoad(() => {
		const app = getApp();
		// 获取并打印authorization值
		const authorization = uni.getStorageSync('authorization');
		console.log('当前页面authorization值:', authorization);

		if (!app.checkLogin()) {
			return;
		} else {
			console.log('登录成功');
			// 初始化并获取推荐列表
			pageNum.value = 1;
			noMore.value = false;
			getRecommendList(1, true);
		}
	});
</script>

<style>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.header {
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 100;
}

.logo-area {
	display: flex;
	align-items: center;
}

.logo {
	width: 60rpx;
	height: 60rpx;
	margin-right: 10rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-right: 20rpx;
}

.refresh-icon {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	border-radius: 50%;
	cursor: pointer;
}

.refresh-text {
	font-size: 32rpx;
	color: #666;
}

.search-box {
	flex: 1;
	margin-left: 20rpx;
	height: 70rpx;
	background-color: #f5f5f5;
	border-radius: 35rpx;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	position: relative;
}

.search-input {
	flex: 1;
	height: 100%;
	font-size: 28rpx;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}

.search-input::placeholder {
	color: #999;
}

.search-icon {
	position: absolute;
	right: 20rpx;
	color: #999;
	font-size: 24rpx;
}

.clear-icon {
	position: absolute;
	right: 20rpx;
	color: #999;
	font-size: 24rpx;
	width: 30rpx;
	height: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ddd;
	border-radius: 50%;
}

.waterfall {
	flex: 1;
	padding: 10rpx;
	padding-top: 16rpx;
	box-sizing: border-box;
}

.waterfall-wrapper {
	display: flex;
	justify-content: space-between;
}

.waterfall-column {
	width: 49%;
}

/* 错开布局，左列稍微向上偏移 */
.left-column {
	margin-top: -20rpx;
}

.waterfall-item {
	border-radius: 12rpx;
	background-color: #fff;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: relative;
}

.media-wrapper {
	position: relative;
	width: 100%;
	overflow: hidden;
}

.item-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.video-icon {
	position: absolute;
	right: 20rpx;
	bottom: 20rpx;
	width: 60rpx;
	height: 60rpx;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

.floating-title {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 16rpx;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
	color: #fff;
	font-weight: bold;
	font-size: 28rpx;
}

.item-content {
	padding: 16rpx;
}

.item-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 16rpx;
}

.item-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 10rpx;
}

.user-info {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	margin-right: 10rpx;
}

.user-name {
	font-size: 22rpx;
	color: #999;
}

.like-info {
	display: flex;
	align-items: center;
}
.icon-rabbit{
	width: 24rpx;
	height: 24rpx;
}

.like-count {
	font-size: 22rpx;
	color: #999;
	margin-left: 4rpx;
}

.loading {
	text-align: center;
	padding: 20rpx 0;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
}

.load-more-btn {
	text-align: center;
	padding: 30rpx 0;
	margin: 20rpx 40rpx;
	background-color: #f8f8f8;
	border-radius: 40rpx;
	border: 1rpx solid #e0e0e0;
}

.load-more-text {
	font-size: 28rpx;
	color: #666;
}

.no-more {
	text-align: center;
	padding: 20rpx 0;
}

.no-more-text {
	font-size: 24rpx;
	color: #999;
}

/* 回到顶部按钮 */
.back-to-top {
	position: fixed;
	right: 30rpx;
	bottom: 120rpx;
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.back-to-top-icon {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
}

/* 添加图标字体类 */
.icon-play:before {
	content: "\e600";
}

.icon-like:before {
	content: "\e601";
}

.icon-search:before {
	content: "\e602";
}
</style>