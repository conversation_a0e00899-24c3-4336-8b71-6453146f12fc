package com.suakitsu.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 应用环境设置
 */
@Component
@ConfigurationProperties(prefix = "app-config")
@Data
public class AppConfig {
    /**
     * 是否调试模式
     */
    private boolean debug;

    /**
     * 验证码配置
     */
    private SmsConfig smsConfig;

    @Data
    public static class SmsConfig {
        /**
         * 发送平台：1钉钉，2阿里云
         */
        private Integer plat;
        /**
         * 验证码长度
         */
        private Integer length;
        /**
         * 发送冷却时间
         */
        private Duration interval;
        /**
         * 最大每日发送次数
         */
        private Integer maxSend;
        /**
         * 有效时长
         */
        private Duration expiration;
    }

    /**
     * 阿里云Oss配置
     */
    private AliOss aliOss;

    @Data
    public static class AliOss {
        /**
         * 主域名
         */
        private String mainUrl;
    }
}