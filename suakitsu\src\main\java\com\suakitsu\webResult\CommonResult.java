package com.suakitsu.webResult;

import lombok.Data;

/**
 * 公用返回类
 */
@Data
public abstract class CommonResult<T> {
    private long code;
    private String msg;
    private T data;

    public static class Web<T> extends CommonResult<T> {
        /**
         * 通用返回
         *
         * @param code 错误码
         * @param msg  提示信息
         * @param data 返回数据
         */
        protected CommonResult<T> result(Integer code, String msg, T data) {
            return new Web<T>()
                .setCode(code)
                .setMsg(msg)
                .setData(data);
        }
    }

    /**
     * 成功返回
     *
     * @param msg  提示信息
     * @param data 返回内容
     */
    public static <T> CommonResult<T> webSuccess(String msg, T data) {
        return new Web<T>().result(0, msg, data);
    }

    /**
     * 成功返回
     * @param data 返回内容
     */
    public static <T> CommonResult<T> webSuccess(T data) {
        return new Web<T>().result(0, "成功", data);
    }

    /**
     * 错误返回
     *
     * @param code 错误码
     * @param msg  错误信息
     */
    public static <T> CommonResult<T> webError(Integer code, String msg) {
        return new Web<T>().result(code, msg, null);
    }
    
}
