package com.suakitsu.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

public interface BaseMapper<T> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<T> {
    default T selectFirst(@Param("ew") LambdaQueryWrapper<T> queryWrapper) {
        return selectOne(queryWrapper.last("limit 1"));
    }

    default Page<T> selectPage(Long page, Long limit, @Param("ew") Wrapper<T> queryWrapper) {
        return selectPage(new Page<>(page, limit), queryWrapper);
    }
}
