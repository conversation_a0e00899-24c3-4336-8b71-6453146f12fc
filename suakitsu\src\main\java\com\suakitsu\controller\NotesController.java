package com.suakitsu.controller;

import com.suakitsu.entity.Notes;
import com.suakitsu.service.Impl.NotesServiceImpl;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/notes")
@CrossOrigin
public class NotesController {
    @Resource
    private NotesServiceImpl notesService;

    // 查询用户的所有笔记
    @GetMapping
    @CrossOrigin
    public CommonResult<Object> getNotes(@RequestParam Long userId) {
        if (userId == null) {
            return CommonResult.webError(-1,"用户不存在");
        }
        return CommonResult.webSuccess("",notesService.getNotes(userId));
    }
    // 添加笔记
    @PostMapping
    @CrossOrigin
    public CommonResult<Object> addNotes(@RequestBody Notes notesModel) {
        if (notesModel == null) {
            return CommonResult.webError(-1,"数据格式错误");
        }
        return CommonResult.webSuccess("",notesService.addNotes(notesModel.getUserId(), notesModel.getTitle(), notesModel.getContent()));
    }
    // 修改笔记
    @PutMapping
    @CrossOrigin
    public CommonResult<Object> updateNotes(Notes notesModel) {
        if (notesModel == null) {
            return CommonResult.webError(-1,"数据格式错误");
        }
        return CommonResult.webSuccess("",notesService.updateNotes(notesModel.getId(),  notesModel.getTitle(), notesModel.getContent(), notesModel.getUserId()));
    }
    // 删除笔记
    @DeleteMapping
    @CrossOrigin
    public CommonResult<Object> deleteNotes(Long id, Long userId) {
        if (id == null || userId == null) {
            return CommonResult.webError(-1,"数据格式错误");
        }
        return CommonResult.webSuccess("",notesService.deleteNotes(id, userId));
    }
}
