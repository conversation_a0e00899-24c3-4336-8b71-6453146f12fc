
.login-page {
	position: relative;
	width: 100%;
	height: 100vh;
	overflow: hidden;
}
.bg-image {
	position: absolute;
	width: 100%;
	height: 100%;
	object-fit: cover;
	opacity: 0.85;
	filter: blur(3px);
	z-index: -1;
}
.login-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 1.875rem 1.5625rem;
	height: 100%;
	box-sizing: border-box;
}
.logo-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 1.875rem;
	margin-bottom: 2.5rem;
}
.logo {
	width: 5.625rem;
	height: 5.625rem;
	border-radius: 0.625rem;
	box-shadow: 0 0.3125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.app-title {
	font-size: 1.5rem;
	font-weight: bold;
	color: #ffffff;
	margin-top: 0.9375rem;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}
.form-container {
	width: 100%;
	background-color: rgba(255, 255, 255, 0.85);
	border-radius: 0.9375rem;
	padding: 1.5625rem 1.25rem;
	box-shadow: 0 0.46875rem 0.9375rem rgba(0, 0, 0, 0.1);
}
.input-item {
	display: flex;
	align-items: center;
	height: 3.125rem;
	background-color: #f6f6f6;
	border-radius: 1.5625rem;
	padding: 0 0.9375rem;
	margin-bottom: 0.9375rem;
}
.placeholder {
	color: #999;
	font-size: 0.875rem;
}
.code-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.5625rem;
}
.code-input {
	flex: 1;
	margin-right: 0.625rem;
	margin-bottom: 0;
}
.send-code-btn {
	width: 6.875rem;
	height: 3.125rem;
	background: linear-gradient(45deg, #ff9a9e, #fad0c4);
	color: #fff;
	border-radius: 1.5625rem;
	font-size: 0.875rem;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 0.3125rem 0.625rem rgba(255, 154, 158, 0.3);
}
.send-code-btn[disabled] {
	background: #cccccc;
	opacity: 0.8;
}
.login-btn {
	width: 100%;
	height: 3.125rem;
	background: linear-gradient(45deg, #a6c1ee, #fbc2eb);
	color: #fff;
	border-radius: 1.5625rem;
	font-size: 1.125rem;
	font-weight: bold;
	box-shadow: 0 0.3125rem 0.625rem rgba(166, 193, 238, 0.3);
	margin-bottom: 1.25rem;
}
.other-login {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 1.5625rem;
}
.other-text {
	color: #999;
	font-size: 0.8125rem;
	margin-bottom: 0.9375rem;
	position: relative;
}
.other-text::before,
.other-text::after {
	content: '';
	position: absolute;
	top: 50%;
	width: 2.5rem;
	height: 1px;
	background-color: #e0e0e0;
}
.other-text::before {
	left: -3.125rem;
}
.other-text::after {
	right: -3.125rem;
}
.icon-group {
	display: flex;
	justify-content: center;
}
.icon-item {
	width: 2.5rem;
	height: 2.5rem;
	background-color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0.9375rem;
	box-shadow: 0 0.15625rem 0.46875rem rgba(0, 0, 0, 0.1);
}
.iconfont {
	color: #666;
	font-size: 1.25rem;
}
.icon-wechat {
	color: #07c160;
}
.icon-qq {
	color: #12b7f5;
}
