package com.suakitsu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户设置实体类
 */
@Data
@Accessors(chain = true)
@TableName("user_setting")
public class UserSetting extends Base {
    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联的用户ID
     */
    private Long userId;
    
    /**
     * 主题设置
     */
    private String theme;
    
    /**
     * 语言设置
     */
    private String language;
    
    /**
     * 通知设置（JSON格式）
     */
    private String notifications;
    
    /**
     * 隐私设置（JSON格式）
     */
    private String privacy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}