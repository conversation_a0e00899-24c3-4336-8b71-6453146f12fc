package com.suakitsu.thirdapi.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.suakitsu.config.AliOssConfig;
import com.suakitsu.config.AppConfig;
import com.suakitsu.utils.CustomMultipartFile;
import com.suakitsu.utils.StringUtil;
import jakarta.annotation.Resource;
import okhttp3.HttpUrl;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

@Service
public class AliyunService {
    @Resource
    private AppConfig appConfig;

    @Resource
    private AliOssConfig ossConfig;


    public static MultipartFile convertBase64ToMultipartFile(String base64Data, String fileName) {
        try {
            // 去除 Base64 前缀（如果有）
            String base64Str = base64Data.replaceFirst("^data:image/\\w+;base64,", "");

            // 解码 Base64
            byte[] decodedBytes = Base64.decodeBase64(base64Str);

            // 创建 CustomMultipartFile
            return new CustomMultipartFile(decodedBytes, "file", fileName, "image/jpeg");

        } catch (Exception e) {
            throw new RuntimeException("Base64 转换为 MultipartFile 失败", e);
        }
    }

    /**
     * 上传文件
     *
     * @param uploadFile 上传文件流
     * @param fileName   文件名/路径
     * @return 是否上传成功
     */
    public boolean uploadFile(MultipartFile uploadFile, String fileName) {
        if (uploadFile == null || fileName == null) {
            return false;
        }

        OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret());

        try {
            InputStream inputStream = uploadFile.getInputStream();
            String objectName = (appConfig.isDebug() ? "debug/" : "") + StringUtil.stripBefore(fileName, "/");
            ossClient.putObject(ossConfig.getBucketName(), objectName, inputStream);
            return true;
        } catch (IOException e) {
            return false;
        } finally {
            ossClient.shutdown();
        }
    }


    /**
     * 上传文件
     *
     * @param uploadFile 上传文件流
     * @param fileName   文件名/路径
     * @return 是否上传成功
     */
    public boolean uploadRecommendFile(MultipartFile uploadFile, String fileName) {
        if (uploadFile == null || fileName == null) {
            return false;
        }

        OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret());

        try {
            InputStream inputStream = uploadFile.getInputStream();
            // 修改这里：添加 daily/ 前缀
            String objectName;
            if (appConfig.isDebug()) {
                objectName = "debug/daily/" + StringUtil.stripBefore(fileName, "/");
            } else {
                objectName = "daily/" + StringUtil.stripBefore(fileName, "/");
            }

            System.out.println("OSS 对象名: " + objectName);

            ossClient.putObject(ossConfig.getBucketName(), objectName, inputStream);
            return true;
        } catch (IOException e) {
            return false;
        } finally {
            ossClient.shutdown();
        }
    }

    /**
     * 上传文件
     *
     * @param uploadFile 上传文件流
     * @param fileName   文件名/路径
     * @return 是否上传成功
     */
    public boolean uploadAvatarFile(MultipartFile uploadFile, String fileName) {
        if (uploadFile == null || fileName == null) {
            return false;
        }

        OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret());

        try {
            InputStream inputStream = uploadFile.getInputStream();
            // 修改这里：添加 daily/ 前缀
            String objectName;
            if (appConfig.isDebug()) {
                objectName = "debug/avatar/" + StringUtil.stripBefore(fileName, "/");
            } else {
                objectName = "avatar/" + StringUtil.stripBefore(fileName, "/");
            }

            System.out.println("OSS 对象名: " + objectName);

            ossClient.putObject(ossConfig.getBucketName(), objectName, inputStream);
            return true;
        } catch (IOException e) {
            return false;
        } finally {
            ossClient.shutdown();
        }
    }


    /**
     * 读取文件地址
     *
     * @param fileName 文件名
     * @return 完整的文件访问URL
     */
    public String loadFileUrl(String fileName) {
        if (Objects.isNull(fileName)) {
            return null;
        }

        fileName = fileName.replaceAll("\\r\\n|\\r|\\n", "");

        HttpUrl.Builder httpBuilder = Objects.requireNonNull(HttpUrl.parse(ossConfig.getDomain())).newBuilder();
        if (appConfig.isDebug()) {
            httpBuilder.addPathSegments("debug");
        }
        httpBuilder.addPathSegments(StringUtil.stripBefore(fileName, "/"));
        return httpBuilder.build().toString();
    }
}
