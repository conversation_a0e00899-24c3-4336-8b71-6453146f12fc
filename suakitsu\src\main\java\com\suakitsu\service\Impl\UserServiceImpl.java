package com.suakitsu.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suakitsu.entity.User;
import com.suakitsu.mapper.UserMapper;
import com.suakitsu.service.UserService;
import com.suakitsu.tools.RandomUtils;
import com.suakitsu.utils.RandomUserInformation;
import com.suakitsu.utils.StringUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Resource
    private UserMapper userMapper;


    /**
     * 获取用户ID
     *
     * @param phone 手机号
     * @return 返回用户ID
     */
    public Long getUserIdByPhone(String phone) {
        // 获取用户
        User userDB = baseMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getPhone, phone));

        if (userDB == null) {
            // 不存在就创建
            userDB = (User) new User()
                    .setNickname("用户" + StringUtil.random("0123456789", 6))
                    .setPhone(phone)
                    .setAvatar(RandomUserInformation.randomUserAvatar())
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            userMapper.insert(userDB);
        }
        return userDB.getId();
    }


    /**
     * 修改用户资料
     *
     * @param userId   用户ID
     * @param nickname 用户昵称
     * @param avatar   头像地址
     */
    public void editUserInfo(Long userId, String nickname, String avatar, String gender, String birthday) {
        // 先从数据库查询原始用户信息
        User originalUser = baseMapper.selectById(userId);
        if (originalUser == null) {
            throw new RuntimeException("用户不存在，无法进行信息更新");
        }

        User user = new User();
        user.setId(userId);

        user.setNickname(nickname != null ? nickname : originalUser.getNickname());
        user.setAvatar(avatar != null ? avatar : originalUser.getAvatar());
        user.setBirthday(birthday != null ? birthday : originalUser.getBirthday());
        user.setGender(gender != null ? gender : originalUser.getGender());
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        baseMapper.updateById(user);
    }

    public User getUserInfoById(Long userId) {
        User user = baseMapper.selectById(userId);

        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        return user;
    }

    /**
     * 用户登录
     *
     * @param user 用户类
     * @return 返回是否登陆成功
     */
    public Object login(User user) {
        String phone = user.getPhone() == null ? "" : user.getPhone();


        User userDB = baseMapper.selectOne(new QueryWrapper<User>().eq("phone", phone));

        if (userDB != null ) {
            return userDB.getId();
        }
        return "登陆失败";
    }
}
