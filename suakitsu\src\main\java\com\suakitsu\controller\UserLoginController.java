package com.suakitsu.controller;

import com.suakitsu.entity.User;
import com.suakitsu.model.UserModel.UserLoginRes;
import com.suakitsu.service.Impl.UserCookieServiceImpl;
import com.suakitsu.service.Impl.UserServiceImpl;
import com.suakitsu.webResult.CommonResult;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/login")
@CrossOrigin
public class UserLoginController {

    @Resource
    private UserServiceImpl userServiceImpl;

    @Resource
    private UserCookieServiceImpl userCookieService;

    @PostMapping("/user")
    public Object login(@RequestBody User user) {
        Object userId = userServiceImpl.login(user);
        if (userId == null) {
            return CommonResult.webSuccess("登录失败，请检查账号密码");
        }
        // 登录账号
        String authorization = userCookieService.addCookie(Long.parseLong(String.valueOf(userId)),true);
        UserLoginRes responseBody = new UserLoginRes()
                .setAuthorization(authorization);
        return CommonResult.webSuccess(responseBody);
    }
}
