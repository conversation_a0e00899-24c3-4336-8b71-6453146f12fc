spring.application.name=suakitsu

# MySQL数据库配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*******************************************************************************************************************************************************************************************
spring.datasource.username=suakitsu
spring.datasource.password=waym3344224

# Hibernate配置
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# 连接池配置
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

# 短信验证码配置
app-config.sms-config.plat=1
app-config.sms-config.length=6
app-config.sms-config.interval=PT60S
app-config.sms-config.max-send=100
app-config.sms-config.expiration=PT300S