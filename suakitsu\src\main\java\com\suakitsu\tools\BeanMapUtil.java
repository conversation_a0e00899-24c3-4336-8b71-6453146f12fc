package com.suakitsu.tools;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;

import java.util.Map;

public class BeanMapUtil {
    /**
     * Bean 转 Map
     *
     * @param beanObject Bean对象
     * @return 转换结果
     */
    public static Map<String, Object> beanToMap(Object beanObject) {
        var objectMapper = new ObjectMapper();
        // 忽略不存在字段
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        JavaType mapClazz = typeFactory.constructParametricType(Map.class, String.class, Object.class);
        try {
            return objectMapper.readValue(objectMapper.writeValueAsString(beanObject), mapClazz);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * Map 转 Bean
     *
     * @param map   需要转换的 Map
     * @param clazz 转出的 Bean 类型
     * @return 转换结果
     */
    public static <T> T mapToBean(Map<String, Object> map, Class<T> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        // 忽略不存在字段
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(objectMapper.writeValueAsString(map), clazz);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
}
