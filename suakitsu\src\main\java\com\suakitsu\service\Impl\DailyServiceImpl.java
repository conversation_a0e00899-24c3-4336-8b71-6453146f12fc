package com.suakitsu.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.suakitsu.mapper.DailyMapper;
import com.suakitsu.entity.Daily;
import com.suakitsu.service.DailyService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Service
public class DailyServiceImpl extends ServiceImpl<DailyMapper, Daily> implements DailyService {

    @Resource
    private DailyMapper dailyMapper;

    // 获取用户的所有日记
    public Object getDaily(Long userId) {
        return dailyMapper.selectList(new LambdaQueryWrapper<Daily>()
                .eq(Daily::getUserId, userId));
    }


    // 添加日记
    public Object addDaily(Long userId, String title, String content, String image) {
        try {
            System.out.println("=== DailyService.addDaily 调试信息 ===");
            System.out.println("userId: " + userId);
            System.out.println("title: " + title);
            System.out.println("content: " + content);
            System.out.println("image: " + image);

            if (userId == null) {
                System.err.println("错误: userId 为 null");
                return false;
            }

            if (title == null || content == null) {
                System.err.println("错误: title 或 content 为 null");
                return false;
            }

            Daily dailyModel = new Daily();
            dailyModel.setTitle(title)
                    .setContent(content)
                    .setImage(image)
                    .setUserId(userId)  // 确保设置了 userId
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            System.out.println("准备插入的 Daily 对象: " + dailyModel);

            int insertResult = baseMapper.insert(dailyModel);
            System.out.println("插入结果: " + insertResult);

            return insertResult >= 1;

        } catch (Exception e) {
            System.err.println("DailyService.addDaily 异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // 删除日记
    public boolean deleteDaily(Long id, Long userId) {
        if (userId == null || id == null) {
            return false;
        }

        // 验证该日记是否属于该用户
        Daily existingDaily = dailyMapper.selectOne(new LambdaQueryWrapper<Daily>()
                .eq(Daily::getId, id)
                .eq(Daily::getUserId, userId));
        
        if (existingDaily == null) {
            return false;
        }

        return dailyMapper.deleteById(id) >= 1;
    }

    // 更新日记
    public boolean updateDaily(Long id, String title, String content, String image, Long userId) {
        if (id == null || userId == null) {
            return false;
        }

        Daily dailyModel = dailyMapper.selectOne(new LambdaQueryWrapper<Daily>().eq(Daily::getId, id).eq(Daily::getUserId, userId));

        if(dailyModel == null){
            return false;
        }
        
        LocalDateTime timeNow = LocalDateTime.now();

        dailyModel.setContent(content)
                .setTitle(title)
                .setImage(image)
                .setUpdateTime(timeNow);
                
        return dailyMapper.updateById(dailyModel) >= 1;
    }
}